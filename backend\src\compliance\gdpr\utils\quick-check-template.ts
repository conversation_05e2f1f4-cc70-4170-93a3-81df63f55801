import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, GdprRuleId, GdprCategory, Severity } from '../types';

export class QuickCheckTemplate {
  /**
   * Create a text analysis check for simple pattern matching
   */
  static async createTextAnalysisCheck(
    ruleId: GdprRuleId,
    ruleName: string,
    category: GdprCategory,
    weight: number,
    severity: Severity,
    searchPatterns: string[],
    config: { targetUrl: string; timeout: number },
    manualReview: boolean = false,
    passingThreshold: number = 50,
  ): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      const analysis = await page.evaluate((patterns: string[]) => {
        const text = document.body.textContent?.toLowerCase() || '';
        const foundPatterns = patterns.filter((pattern: string) =>
          text.includes(pattern.toLowerCase()),
        );
        return { foundPatterns, totalPatterns: patterns.length };
      }, searchPatterns);

      const score = Math.round((analysis.foundPatterns.length / analysis.totalPatterns) * 100);

      if (analysis.foundPatterns.length > 0) {
        evidence.push({
          type: 'text',
          description: `${ruleName} content found`,
          value: `Detected: ${analysis.foundPatterns.join(', ')}`,
        });
      } else {
        evidence.push({
          type: 'text',
          description: `${ruleName} content not found`,
          value: `Missing patterns: ${searchPatterns.join(', ')}`,
        });
      }

      const passed = score >= passingThreshold;

      return {
        ruleId,
        ruleName,
        category,
        passed,
        score,
        weight,
        severity,
        evidence,
        recommendations: passed
          ? []
          : [
              {
                priority: 1,
                title: `Add ${ruleName.toLowerCase()} information`,
                description: `Include required ${ruleName.toLowerCase()} content`,
                implementation: `Update privacy policy or website content with ${ruleName.toLowerCase()} details`,
                effort: 'moderate' as const,
                impact: 'medium' as const,
              },
            ],
        manualReviewRequired: manualReview,
      };
    } catch (error) {
      return {
        ruleId,
        ruleName,
        category,
        passed: false,
        score: 0,
        weight,
        severity,
        evidence: [
          {
            type: 'text',
            description: 'Check failed',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: `Fix ${ruleName}`,
            description: 'Resolve implementation issues',
            implementation: 'Review and fix check implementation',
            effort: 'moderate' as const,
            impact: 'medium' as const,
          },
        ],
        manualReviewRequired: manualReview,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Create a manual review check that flags for legal assessment
   */
  static createManualReviewCheck(
    ruleId: GdprRuleId,
    ruleName: string,
    category: GdprCategory,
    weight: number,
    severity: Severity,
    description: string,
    legalRequirement: string,
  ): GdprCheckResult {
    return {
      ruleId,
      ruleName,
      category,
      passed: false, // Manual review items don't auto-pass
      score: 0, // No automated score
      weight,
      severity,
      evidence: [
        {
          type: 'text',
          description: 'Manual review required',
          value: description,
        },
      ],
      recommendations: [
        {
          priority: 1,
          title: 'Manual legal review required',
          description: `${ruleName} requires legal expertise assessment`,
          implementation: `Have legal expert review ${legalRequirement}`,
          effort: 'moderate' as const,
          impact: 'high' as const,
        },
      ],
      manualReviewRequired: true,
    };
  }
}
