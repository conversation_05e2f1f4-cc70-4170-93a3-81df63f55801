/**
 * Manual Review Template System
 * STRICT SEPARATION: Automated checks with separate manual review item tracking
 */

import { Page } from 'puppeteer';
import {
  WcagCheckResult,
  WcagEvidence,
  // WcagManualReviewItem,
  WcagCategory,
  WcagLevel,
} from '../types';
import { CheckTemplate, CheckConfig } from './check-template';

export interface ManualReviewConfig extends CheckConfig {
  enableManualTracking: boolean;
  maxManualItems: number;
}

export interface ManualReviewItem {
  selector: string;
  description: string;
  automatedFindings: string;
  reviewRequired: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number; // minutes
  type?: string; // Additional type field for categorization
  element?: string; // Element information
  context?: string; // Additional context field for compatibility
}

export type ManualReviewCheckFunction<T extends ManualReviewConfig> = (
  page: Page,
  config: T,
) => Promise<{
  automatedScore: number;
  maxScore: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
  manualReviewItems: ManualReviewItem[];
  automationRate: number;
}>;

export class ManualReviewTemplate extends CheckTemplate {
  /**
   * Execute a WCAG check with manual review capabilities
   * STRICT SEPARATION: Automated results completely separate from manual review items
   */
  async executeManualReviewCheck<T extends ManualReviewConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    automationRate: number,
    config: T,
    checkFunction: ManualReviewCheckFunction<T>,
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();

    try {
      console.log(
        `🔍 [${config.scanId}] Starting ${ruleId}: ${ruleName} (${Math.round(automationRate * 100)}% automated)`,
      );

      if (!config.page) {
        throw new Error('Browser instance required - page not provided in config');
      }

      // Execute the specific check function
      const result = await checkFunction(config.page, config);

      const executionTime = Date.now() - startTime;

      // Determine status based on automated findings ONLY
      // Manual review items are tracked separately and don't affect automated score
      let status: 'passed' | 'failed' | 'manual_review';

      if (result.manualReviewItems.length === 0) {
        // Pure automated result
        status = result.automatedScore === result.maxScore ? 'passed' : 'failed';
      } else {
        // Has manual review items - status based on automated portion only
        status = result.automatedScore === result.maxScore ? 'passed' : 'failed';

        // Add manual review evidence for tracking (separate from scoring)
        result.evidence.push({
          type: 'text',
          description: 'Manual review items identified',
          value: `${result.manualReviewItems.length} items require manual verification (separate from automated score)`,
          severity: 'info',
        });

        // Add individual manual review items as evidence for tracking
        result.manualReviewItems.forEach((item) => {
          result.evidence.push({
            type: 'text',
            description: `Manual review: ${item.description}`,
            value: `Automated findings: ${item.automatedFindings} | Review needed: ${item.reviewRequired}`,
            selector: item.selector,
            severity: item.priority === 'high' ? 'warning' : 'info',
          });
        });
      }

      console.log(
        `✅ [${config.scanId}] Completed ${ruleId} in ${executionTime}ms (Score: ${result.automatedScore}/${result.maxScore}, Manual items: ${result.manualReviewItems.length})`,
      );

      return {
        ruleId,
        ruleName,
        category: category as WcagCategory,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as WcagLevel,
        status,
        score: result.automatedScore, // Only automated score affects final score
        maxScore: result.maxScore,
        weight,
        automated: true, // Still considered automated even with manual review tracking
        evidence: result.evidence,
        recommendations: result.recommendations,
        executionTime,
        // Store manual review items separately for tracking purposes
        manualReviewItems: result.manualReviewItems.map((item) => ({
          selector: item.selector,
          description: item.description,
          automatedFindings: item.automatedFindings,
          reviewRequired: item.reviewRequired,
          priority: item.priority,
          estimatedTime: item.estimatedTime,
        })),
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ [${config.scanId}] Error in ${ruleId}:`, error);

      return {
        ruleId,
        ruleName,
        category: category as WcagCategory,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as WcagLevel,
        status: 'failed',
        score: 0,
        maxScore: 100,
        weight,
        automated: true,
        evidence: [],
        recommendations: ['Check failed due to technical error - manual review recommended'],
        executionTime,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Create a manual review item with proper structure
   */
  static createManualReviewItem(
    selector: string,
    description: string,
    automatedFindings: string,
    reviewRequired: string,
    priority: 'high' | 'medium' | 'low' = 'medium',
    estimatedTime: number = 3,
  ): ManualReviewItem {
    return {
      selector,
      description,
      automatedFindings,
      reviewRequired,
      priority,
      estimatedTime,
    };
  }

  /**
   * Validate manual review configuration
   */
  static validateManualReviewConfig(config: ManualReviewConfig): boolean {
    if (!config.enableManualTracking) {
      return true; // Manual tracking disabled, no validation needed
    }

    if (config.maxManualItems <= 0) {
      console.warn('maxManualItems should be greater than 0 when manual tracking is enabled');
      return false;
    }

    return true;
  }
}
