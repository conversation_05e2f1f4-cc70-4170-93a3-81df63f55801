# GDPR Implementation Plan - Part 01: Foundation Setup

## Overview
This document provides step-by-step instructions for setting up the foundational infrastructure for GDPR compliance scanning. This includes database schema, core types, and basic project structure.

## ⚠️ CRITICAL REQUIREMENTS
- **NO MOCK DATA**: All implementations must use real website scanning
- **Real Backend Integration**: Connect to actual scanning engines and databases
- **TypeScript Strict Mode**: Follow .projectrules - NO `any` types allowed
- **Zod Validation**: All API inputs must be validated with Zod schemas

## Prerequisites
- Completed HIPAA implementation (reference architecture)
- PostgreSQL database running
- Node.js 18+ and npm 9+
- Docker and docker-compose setup

## Step 1: Database Schema Implementation

### 1.1 Create GDPR Migration File
Create the migration file for GDPR-specific tables:

```bash
# Navigate to backend directory
cd backend

# Create new migration
npx knex migrate:make create_gdpr_tables --knexfile knexfile.ts
```

### 1.2 Implement GDPR Database Schema
Edit the generated migration file (`migrations/YYYYMMDD_create_gdpr_tables.ts`):

```typescript
import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // GDPR Scans Table
  await knex.schema.createTable('gdpr_scans', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('target_url', 2048).notNullable();
    table.timestamp('scan_timestamp', { useTz: true }).defaultTo(knex.fn.now());
    table.integer('scan_duration'); // milliseconds
    table.decimal('overall_score', 5, 2);
    table.enu('risk_level', ['critical', 'high', 'medium', 'low']);
    table.integer('total_checks').notNullable();
    table.integer('passed_checks').notNullable();
    table.integer('failed_checks').notNullable();
    table.integer('manual_review_required').defaultTo(0);
    table.enu('scan_status', ['pending', 'running', 'completed', 'failed']).defaultTo('pending');
    table.text('error_message');
    table.jsonb('metadata'); // scan configuration and technical details
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('user_id');
    table.index('scan_timestamp');
    table.index('scan_status');
    table.index('target_url');
  });

  // GDPR Check Results Table
  await knex.schema.createTable('gdpr_check_results', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('rule_id', 50).notNullable(); // e.g., 'GDPR-001', 'GDPR-002'
    table.string('rule_name', 200).notNullable();
    table.string('category', 100).notNullable(); // e.g., 'consent', 'privacy_policy', 'cookies'
    table.boolean('passed').notNullable();
    table.decimal('score', 5, 2);
    table.decimal('weight', 3, 2);
    table.enu('severity', ['critical', 'high', 'medium', 'low']);
    table.boolean('manual_review_required').defaultTo(false);
    table.jsonb('evidence'); // detailed findings and evidence
    table.jsonb('recommendations'); // improvement suggestions
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('rule_id');
    table.index('category');
    table.index('passed');
  });

  // GDPR Cookie Analysis Table
  await knex.schema.createTable('gdpr_cookie_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('cookie_name', 255).notNullable();
    table.string('cookie_domain', 255).notNullable();
    table.string('cookie_category', 50); // essential, analytics, marketing, etc.
    table.boolean('has_consent');
    table.boolean('secure_flag');
    table.boolean('httponly_flag');
    table.string('samesite_attribute', 20);
    table.timestamp('expiry_date', { useTz: true });
    table.text('purpose');
    table.boolean('third_party').defaultTo(false);
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('cookie_category');
    table.index('third_party');
  });

  // GDPR Consent Analysis Table
  await knex.schema.createTable('gdpr_consent_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('consent_type', 50); // banner, form, checkbox
    table.string('consent_mechanism', 100); // opt-in, opt-out, granular
    table.boolean('has_reject_option');
    table.boolean('has_granular_options');
    table.boolean('pre_ticked_boxes');
    table.text('consent_text');
    table.boolean('privacy_policy_linked');
    table.boolean('compliant');
    table.jsonb('issues'); // array of compliance issues
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('consent_type');
    table.index('compliant');
  });

  // GDPR Tracker Analysis Table
  await knex.schema.createTable('gdpr_tracker_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('tracker_domain', 255).notNullable();
    table.string('tracker_type', 100); // analytics, advertising, social, etc.
    table.string('tracker_name', 255);
    table.boolean('loads_before_consent');
    table.boolean('has_consent_mechanism');
    table.text('data_transferred'); // description of data being transferred
    table.boolean('privacy_policy_mentioned');
    table.boolean('compliant');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('tracker_type');
    table.index('compliant');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('gdpr_tracker_analysis');
  await knex.schema.dropTableIfExists('gdpr_consent_analysis');
  await knex.schema.dropTableIfExists('gdpr_cookie_analysis');
  await knex.schema.dropTableIfExists('gdpr_check_results');
  await knex.schema.dropTableIfExists('gdpr_scans');
}
```

### 1.3 Run Migration
```bash
# Run the migration
npm run migrate:latest

# Verify tables were created
npm run db:status
```

## Step 2: Core TypeScript Types

### 2.1 Create GDPR Types File
Create `backend/src/compliance/gdpr/types.ts`:

```typescript
// GDPR Core Types - Strict TypeScript following .projectrules
// NO any[] types allowed - all arrays must specify element types

import { z } from 'zod';

// Core GDPR Rule Identifiers
export type GdprRuleId = 
  | 'GDPR-001' | 'GDPR-002' | 'GDPR-003' | 'GDPR-004' | 'GDPR-005'
  | 'GDPR-006' | 'GDPR-007' | 'GDPR-008' | 'GDPR-009' | 'GDPR-010'
  | 'GDPR-011' | 'GDPR-012' | 'GDPR-013' | 'GDPR-014' | 'GDPR-015'
  | 'GDPR-016' | 'GDPR-017' | 'GDPR-018' | 'GDPR-019' | 'GDPR-020'
  | 'GDPR-021';

export type GdprCategory = 
  | 'security' | 'privacy_policy' | 'consent' | 'cookies' 
  | 'data_rights' | 'data_protection' | 'organizational';

export type Severity = 'critical' | 'high' | 'medium' | 'low';
export type RiskLevel = 'critical' | 'high' | 'medium' | 'low';
export type ScanStatus = 'pending' | 'running' | 'completed' | 'failed';

// Zod Schemas for Validation
export const GdprScanRequestSchema = z.object({
  targetUrl: z.string().url('Valid URL is required'),
  scanOptions: z.object({
    enableCookieAnalysis: z.boolean().optional().default(true),
    enableTrackerDetection: z.boolean().optional().default(true),
    enableConsentTesting: z.boolean().optional().default(true),
    maxPages: z.number().int().min(1).max(50).optional().default(10),
    timeout: z.number().int().min(60000).max(3600000).optional().default(300000),
  }).optional().default({}),
});

export type GdprScanRequest = z.infer<typeof GdprScanRequestSchema>;

// Core Interfaces
export interface GdprScanResult {
  scanId: string;
  targetUrl: string;
  timestamp: string;
  scanDuration: number;
  overallScore: number;
  riskLevel: RiskLevel;
  status: ScanStatus;
  summary: GdprScanSummary;
  checks: GdprCheckResult[];
  recommendations: GdprRecommendation[];
  metadata: GdprScanMetadata;
}

export interface GdprScanSummary {
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  manualReviewRequired: number;
  criticalFailures: number;
  categoryBreakdown: CategoryBreakdown[];
}

export interface CategoryBreakdown {
  category: GdprCategory;
  score: number;
  checksInCategory: number;
  passedInCategory: number;
}

export interface GdprCheckResult {
  ruleId: GdprRuleId;
  ruleName: string;
  category: GdprCategory;
  passed: boolean;
  score: number;
  weight: number;
  severity: Severity;
  evidence: Evidence[];
  recommendations: Recommendation[];
  manualReviewRequired: boolean;
}

export interface Evidence {
  type: 'text' | 'element' | 'network' | 'cookie';
  description: string;
  location?: string;
  value?: string;
}

export interface Recommendation {
  priority: number;
  title: string;
  description: string;
  implementation: string;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
}

export interface GdprRecommendation {
  id: string;
  priority: number;
  title: string;
  description: string;
  category: GdprCategory;
  effort: 'minimal' | 'moderate' | 'significant';
  impact: 'low' | 'medium' | 'high';
  timeline: string;
  relatedRules: GdprRuleId[];
}

export interface GdprScanMetadata {
  version: string;
  processingTime: number;
  checksPerformed: number;
  analysisLevelsUsed: string[];
  errors: string[];
  warnings: string[];
  userAgent: string;
  scanOptions: GdprScanRequest['scanOptions'];
}

// Cookie-specific types
export interface Cookie {
  name: string;
  value: string;
  domain: string;
  path: string;
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'Strict' | 'Lax' | 'None' | undefined;
  expires?: Date;
}

export interface CookieAnalysisResult {
  cookieId: string;
  category: 'essential' | 'analytics' | 'marketing' | 'functional';
  hasConsent: boolean;
  complianceIssues: string[];
  recommendations: string[];
}

// Consent-specific types
export interface ConsentBannerInfo {
  isPresent: boolean;
  bannerText: string;
  hasAcceptButton: boolean;
  hasRejectButton: boolean;
  hasGranularOptions: boolean;
  privacyPolicyLinked: boolean;
  complianceIssues: string[];
}

// Tracker-specific types
export interface Tracker {
  domain: string;
  name: string;
  category: TrackerCategory;
  purpose: string;
  dataCollected: string[];
  hasConsentMechanism: boolean;
  loadedBeforeConsent: boolean;
}

export type TrackerCategory = 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';

// Database entity types
export interface GdprScanEntity {
  id: string;
  user_id: string;
  target_url: string;
  scan_timestamp: Date;
  scan_duration: number;
  overall_score: number;
  risk_level: RiskLevel;
  total_checks: number;
  passed_checks: number;
  failed_checks: number;
  manual_review_required: number;
  scan_status: ScanStatus;
  error_message?: string;
  metadata: Record<string, unknown>;
  created_at: Date;
  updated_at: Date;
}

export interface GdprCheckResultEntity {
  id: string;
  scan_id: string;
  rule_id: GdprRuleId;
  rule_name: string;
  category: GdprCategory;
  passed: boolean;
  score: number;
  weight: number;
  severity: Severity;
  manual_review_required: boolean;
  evidence: Record<string, unknown>;
  recommendations: Record<string, unknown>;
  created_at: Date;
}
```

## Step 3: Project Structure Setup

### 3.1 Create GDPR Module Directory Structure
```bash
# Navigate to backend compliance directory
cd backend/src/compliance

# Create GDPR module structure
mkdir -p gdpr/{checks,utils,services,database}
mkdir -p gdpr/__tests__/{checks,utils,services}

# Create initial files
touch gdpr/index.ts
touch gdpr/orchestrator.ts
touch gdpr/constants.ts
touch gdpr/database/gdpr-database.ts
```

### 3.2 Create Frontend GDPR Structure
```bash
# Navigate to frontend
cd frontend

# Create GDPR frontend structure
mkdir -p app/dashboard/gdpr/{scan,results}
mkdir -p components/gdpr
mkdir -p types
mkdir -p services

# Create initial files
touch app/dashboard/gdpr/page.tsx
touch components/gdpr/index.ts
touch types/gdpr.ts
touch services/gdpr-api.ts
```

## Next Steps
Continue with Part 02: Core Services Implementation to build the scanning engine and orchestrator.

## Validation Checklist
- [ ] Database migration runs successfully
- [ ] All tables created with proper indexes
- [ ] TypeScript types compile without errors
- [ ] No `any` types used (strict compliance with .projectrules)
- [ ] Zod schemas validate correctly
- [ ] Directory structure matches HIPAA patterns
- [ ] Ready for real website scanning implementation
