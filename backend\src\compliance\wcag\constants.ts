/**
 * WCAG Constants and Configuration
 * STRICT SEPARATION: Automated vs Manual rules
 * Following HIPAA/GDPR patterns with single automated scoring
 */

import { WcagRuleConfig, WcagCategory, WcagVersion, WcagScanOptions } from './types';

// AUTOMATED WCAG Rules - ONLY these contribute to main score
export const WCAG_AUTOMATED_RULES: WcagRuleConfig[] = [
  // WCAG 2.1 Automated Rules (high automation level only)
  {
    ruleId: 'WCAG-001',
    ruleName: 'Non-text Content',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.1.1',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'All non-text content has text alternatives',
    checkFunction: 'NonTextContentCheck',
  },
  {
    ruleId: 'WCAG-002',
    ruleName: 'Captions (Prerecorded)',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.2.2',
    level: 'A',
    weight: 0.0458,
    automated: true,
    description: 'Captions provided for prerecorded audio content',
    checkFunction: 'CaptionsCheck',
  },
  {
    ruleId: 'WCAG-003',
    ruleName: 'Info and Relationships',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.3.1',
    level: 'A',
    weight: 0.0687,
    automated: true,
    description:
      'Information and relationships conveyed through presentation can be programmatically determined',
    checkFunction: 'InfoRelationshipsCheck',
  },
  {
    ruleId: 'WCAG-004',
    ruleName: 'Contrast (Minimum)',
    category: 'perceivable',
    wcagVersion: '2.1',
    successCriterion: '1.4.3',
    level: 'AA',
    weight: 0.0763,
    automated: true,
    description: 'Text has sufficient contrast ratio',
    checkFunction: 'ContrastMinimumCheck',
  },
  {
    ruleId: 'WCAG-005',
    ruleName: 'Keyboard',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.1.1',
    level: 'A',
    weight: 0.0916,
    automated: true,
    description: 'All functionality available from keyboard',
    checkFunction: 'KeyboardCheck',
  },
  {
    ruleId: 'WCAG-006',
    ruleName: 'Focus Order',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.3',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'Focusable components receive focus in logical order',
    checkFunction: 'FocusOrderCheck',
  },
  {
    ruleId: 'WCAG-007',
    ruleName: 'Focus Visible',
    category: 'operable',
    wcagVersion: '2.1',
    successCriterion: '2.4.7',
    level: 'AA',
    weight: 0.0687,
    automated: true,
    description: 'Keyboard focus indicator is visible',
    checkFunction: 'FocusVisibleCheck',
  },
  {
    ruleId: 'WCAG-008',
    ruleName: 'Error Identification',
    category: 'understandable',
    wcagVersion: '2.1',
    successCriterion: '3.3.1',
    level: 'A',
    weight: 0.0534,
    automated: true,
    description: 'Input errors are identified and described to user',
    checkFunction: 'ErrorIdentificationCheck',
  },
  {
    ruleId: 'WCAG-009',
    ruleName: 'Name, Role, Value',
    category: 'robust',
    wcagVersion: '2.1',
    successCriterion: '4.1.2',
    level: 'A',
    weight: 0.0611,
    automated: true,
    description: 'UI components have accessible name, role, and value',
    checkFunction: 'NameRoleValueCheck',
  },

  // WCAG 2.2 New Rules (7 rules)
  {
    ruleId: 'WCAG-010',
    ruleName: 'Focus Not Obscured (Minimum)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.11',
    level: 'AA',
    weight: 0.0458,
    automated: true,
    description: 'Focused element is not fully hidden by author content',
    checkFunction: 'FocusNotObscuredMinCheck',
  },
  {
    ruleId: 'WCAG-011',
    ruleName: 'Focus Not Obscured (Enhanced)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.12',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Focused element is never hidden by author content',
    checkFunction: 'FocusNotObscuredEnhCheck',
  },
  {
    ruleId: 'WCAG-012',
    ruleName: 'Focus Appearance',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.4.13',
    level: 'AAA',
    weight: 0.0305,
    automated: true,
    description: 'Focus indicator meets size and contrast requirements',
    checkFunction: 'FocusAppearanceCheck',
  },
  {
    ruleId: 'WCAG-013',
    ruleName: 'Dragging Movements',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.5.7',
    level: 'AA',
    weight: 0.0382,
    automated: true,
    description: 'Dragging movements have single pointer alternative',
    checkFunction: 'DraggingMovementsCheck',
  },
  {
    ruleId: 'WCAG-014',
    ruleName: 'Target Size (Minimum)',
    category: 'operable',
    wcagVersion: '2.2',
    successCriterion: '2.5.8',
    level: 'AA',
    weight: 0.0458,
    automated: true,
    description: 'Target size is at least 24×24 CSS pixels',
    checkFunction: 'TargetSizeCheck',
  },
  {
    ruleId: 'WCAG-015',
    ruleName: 'Consistent Help',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.2.6',
    level: 'A',
    weight: 0.0305,
    automated: true,
    description: 'Help mechanisms appear in consistent order',
    checkFunction: 'ConsistentHelpCheck',
  },
  {
    ruleId: 'WCAG-016',
    ruleName: 'Redundant Entry',
    category: 'understandable',
    wcagVersion: '2.2',
    successCriterion: '3.3.7',
    level: 'A',
    weight: 0.0382,
    automated: true,
    description: 'Information previously entered is auto-populated',
    checkFunction: 'RedundantEntryCheck',
  },

  // WCAG 3.0 Draft Rules (5 rules)
  {
    ruleId: 'WCAG-017',
    ruleName: 'Image Alternatives',
    category: 'perceivable',
    wcagVersion: '3.0',
    successCriterion: '2.1',
    level: 'A',
    weight: 0.0305,
    automated: true,
    description: 'Enhanced image alternative requirements',
    checkFunction: 'ImageAlternativesCheck',
  },
  {
    ruleId: 'WCAG-018',
    ruleName: 'Text and Wording',
    category: 'understandable',
    wcagVersion: '3.0',
    successCriterion: '2.2',
    level: 'AA',
    weight: 0.0305,
    automated: true,
    description: 'Content uses plain language',
    checkFunction: 'TextWordingCheck',
  },
  {
    ruleId: 'WCAG-019',
    ruleName: 'Keyboard Focus',
    category: 'operable',
    wcagVersion: '3.0',
    successCriterion: '2.4',
    level: 'A',
    weight: 0.0382,
    automated: true,
    description: 'Enhanced keyboard focus requirements',
    checkFunction: 'KeyboardFocusCheck',
  },
  {
    ruleId: 'WCAG-020',
    ruleName: 'Motor',
    category: 'operable',
    wcagVersion: '3.0',
    successCriterion: '2.5',
    level: 'AA',
    weight: 0.0305,
    automated: true,
    description: 'Motor accessibility requirements',
    checkFunction: 'MotorCheck',
  },
  {
    ruleId: 'WCAG-021',
    ruleName: 'Pronunciation & Meaning',
    category: 'understandable',
    wcagVersion: '3.0',
    successCriterion: '3.1',
    level: 'AAA',
    weight: 0.0229,
    automated: true,
    description: 'Unusual words are defined',
    checkFunction: 'PronunciationMeaningCheck',
  },
];

// MANUAL REVIEW RULES - tracked separately, NO scoring contribution
export const WCAG_MANUAL_RULES = [
  {
    ruleId: 'WCAG-MANUAL-001',
    ruleName: 'Content Quality Review',
    category: 'understandable',
    description: 'Manual review of content clarity and comprehension',
    priority: 'medium',
    estimatedTime: 15, // minutes
  },
  {
    ruleId: 'WCAG-MANUAL-002',
    ruleName: 'User Experience Flow',
    category: 'operable',
    description: 'Manual testing of complete user workflows',
    priority: 'high',
    estimatedTime: 30,
  },
  {
    ruleId: 'WCAG-MANUAL-003',
    ruleName: 'Context-Specific Accessibility',
    category: 'perceivable',
    description: 'Manual review of context-dependent accessibility features',
    priority: 'medium',
    estimatedTime: 20,
  },
] as const;

// AUTOMATED SCORING ONLY - Single score system
export const AUTOMATED_CATEGORY_WEIGHTS: Record<WcagCategory, number> = {
  perceivable: 0.25,
  operable: 0.35,
  understandable: 0.25,
  robust: 0.15,
};

// AUTOMATED VERSION WEIGHTS - Single score system
export const AUTOMATED_VERSION_WEIGHTS: Record<WcagVersion, number> = {
  '2.1': 0.5,
  '2.2': 0.35,
  '3.0': 0.15,
};

// AUTOMATED LEVEL REQUIREMENTS - Single score system
export const AUTOMATED_LEVEL_REQUIREMENTS = {
  A: 0.8, // 80% of automated Level A rules must pass
  AA: 0.75, // 75% of automated Level A + AA rules must pass
  AAA: 0.7, // 70% of all automated rules must pass
};

// Aliases for Part 7 compatibility
export const WCAG_RULES = WCAG_AUTOMATED_RULES;
export const CATEGORY_WEIGHTS = AUTOMATED_CATEGORY_WEIGHTS;
export const VERSION_WEIGHTS = AUTOMATED_VERSION_WEIGHTS;
export const LEVEL_REQUIREMENTS = AUTOMATED_LEVEL_REQUIREMENTS;

// MANUAL REVIEW PRIORITIES - separate tracking
export const MANUAL_REVIEW_PRIORITIES = {
  high: 1.0,
  medium: 0.7,
  low: 0.4,
} as const;

// Risk Level Thresholds
export const RISK_THRESHOLDS = {
  critical: 40, // 0-40 score
  high: 60, // 41-60 score
  medium: 80, // 61-80 score
  low: 100, // 81-100 score
};

// Automation Levels by Rule
export const AUTOMATION_LEVELS: Record<string, number> = {
  'WCAG-001': 0.95, // Non-text Content
  'WCAG-002': 0.8, // Captions
  'WCAG-003': 0.9, // Info and Relationships
  'WCAG-004': 1.0, // Contrast (Minimum)
  'WCAG-005': 0.85, // Keyboard
  'WCAG-006': 0.75, // Focus Order
  'WCAG-007': 1.0, // Focus Visible
  'WCAG-008': 0.9, // Error Identification
  'WCAG-009': 0.9, // Name, Role, Value
  'WCAG-010': 1.0, // Focus Not Obscured (Minimum)
  'WCAG-011': 1.0, // Focus Not Obscured (Enhanced)
  'WCAG-012': 1.0, // Focus Appearance
  'WCAG-013': 0.7, // Dragging Movements
  'WCAG-014': 1.0, // Target Size
  'WCAG-015': 0.8, // Consistent Help
  'WCAG-016': 0.85, // Redundant Entry
  'WCAG-017': 0.95, // Image Alternatives (3.0)
  'WCAG-018': 0.75, // Text and Wording (3.0)
  'WCAG-019': 0.9, // Keyboard Focus (3.0)
  'WCAG-020': 0.8, // Motor (3.0)
  'WCAG-021': 0.6, // Pronunciation & Meaning (3.0)
};

// Default Scan Configuration
export const DEFAULT_SCAN_CONFIG: Required<WcagScanOptions> = {
  enableContrastAnalysis: true,
  enableKeyboardTesting: true,
  enableFocusAnalysis: true,
  enableSemanticValidation: true,
  wcagVersion: 'all',
  level: 'AA',
  maxPages: 5,
  timeout: 30000,
};

// Error Codes
export const WCAG_ERROR_CODES = {
  INVALID_URL: 'WCAG_INVALID_URL',
  SCAN_TIMEOUT: 'WCAG_SCAN_TIMEOUT',
  NETWORK_ERROR: 'WCAG_NETWORK_ERROR',
  BROWSER_ERROR: 'WCAG_BROWSER_ERROR',
  VALIDATION_ERROR: 'WCAG_VALIDATION_ERROR',
  DATABASE_ERROR: 'WCAG_DATABASE_ERROR',
  AUTHENTICATION_ERROR: 'WCAG_AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR: 'WCAG_RATE_LIMIT_ERROR',
} as const;

// Success Messages
export const WCAG_SUCCESS_MESSAGES = {
  SCAN_STARTED: 'WCAG scan initiated successfully',
  SCAN_COMPLETED: 'WCAG scan completed successfully',
  RESULTS_RETRIEVED: 'WCAG scan results retrieved successfully',
  EXPORT_GENERATED: 'WCAG report exported successfully',
} as const;
