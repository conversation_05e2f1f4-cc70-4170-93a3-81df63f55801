/**
 * WCAG Rule 4: Contrast (Minimum) - 1.4.3
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { ColorAnalyzer } from '../utils/color-analyzer';
import { WcagEvidence } from '../types';

export interface ContrastCheckConfig extends CheckConfig {
  includeImages?: boolean;
  checkAllElements?: boolean;
}

export class ContrastMinimumCheck {
  private checkTemplate = new CheckTemplate();

  /**
   * Perform contrast minimum check - 100% automated
   */
  async performCheck(config: ContrastCheckConfig) {
    return this.checkTemplate.executeCheck(
      'WCAG-004',
      'Contrast (Minimum)',
      'perceivable',
      0.1,
      'AA',
      config,
      this.executeContrastCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );
  }

  /**
   * Execute contrast analysis on all text elements
   */
  private async executeContrastCheck(page: Page, _config: ContrastCheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Get all text elements with computed styles
    const textElements = await page.evaluate(() => {
      const elements: Array<{
        selector: string;
        text: string;
        foregroundColor: string;
        backgroundColor: string;
        fontSize: string;
        fontWeight: string;
        tagName: string;
        isVisible: boolean;
      }> = [];

      // Helper function to get effective background color
      function getEffectiveBackgroundColor(element: HTMLElement): string {
        let current = element;

        while (current && current !== document.body) {
          const style = window.getComputedStyle(current);
          const bgColor = style.backgroundColor;

          if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
            return bgColor;
          }
          current = current.parentElement as HTMLElement;
        }

        return '#ffffff'; // Default to white
      }

      // Helper function to generate selector
      function generateSelector(element: HTMLElement, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className.split(' ').filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      // Get all elements with text content
      const allElements = document.querySelectorAll('*');

      allElements.forEach((element, index) => {
        const htmlElement = element as HTMLElement;
        const computedStyle = window.getComputedStyle(htmlElement);

        // Check if element has direct text content (not just child text)
        const hasDirectText = Array.from(htmlElement.childNodes).some(
          (node) => node.nodeType === Node.TEXT_NODE && node.textContent?.trim(),
        );

        if (hasDirectText) {
          const isVisible =
            computedStyle.display !== 'none' &&
            computedStyle.visibility !== 'hidden' &&
            computedStyle.opacity !== '0';

          if (isVisible) {
            elements.push({
              selector: generateSelector(htmlElement, index),
              text: htmlElement.textContent?.trim() || '',
              foregroundColor: computedStyle.color,
              backgroundColor: getEffectiveBackgroundColor(htmlElement),
              fontSize: computedStyle.fontSize,
              fontWeight: computedStyle.fontWeight,
              tagName: htmlElement.tagName.toLowerCase(),
              isVisible,
            });
          }
        }
      });

      return elements;
    });

    let totalElements = 0;
    let passedElements = 0;

    // Analyze contrast for each text element
    for (const element of textElements) {
      if (element.text.length < 3) continue; // Skip very short text

      totalElements++;

      const isLargeText = ColorAnalyzer.isLargeText(element.fontSize, element.fontWeight);
      const contrastResult = ColorAnalyzer.analyzeContrast(
        element.foregroundColor,
        element.backgroundColor,
        isLargeText,
      );

      if (contrastResult.passes) {
        passedElements++;

        evidence.push({
          type: 'measurement',
          description: `Text contrast passes ${contrastResult.level} standards`,
          value: `Contrast ratio: ${contrastResult.ratio}:1 (${isLargeText ? 'large' : 'normal'} text)`,
          selector: element.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Low contrast on ${element.selector}: ${contrastResult.ratio}:1`);

        evidence.push({
          type: 'measurement',
          description: 'Text contrast fails WCAG standards',
          value: `Contrast ratio: ${contrastResult.ratio}:1 (required: ${isLargeText ? '3.0' : '4.5'}:1)`,
          selector: element.selector,
          severity: 'error',
        });

        if (contrastResult.recommendation) {
          recommendations.push(`${element.selector}: ${contrastResult.recommendation}`);
        }
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Contrast analysis summary',
      value: `${passedElements}/${totalElements} text elements pass contrast requirements`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift('Review and improve color contrast for better accessibility');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Get effective background color by traversing parent elements
   */
  private getEffectiveBackgroundColor(element: HTMLElement): string {
    let current = element;

    while (current && current !== document.body) {
      const style = window.getComputedStyle(current);
      const bgColor = style.backgroundColor;

      if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
        return bgColor;
      }
      current = current.parentElement as HTMLElement;
    }

    return '#ffffff'; // Default to white
  }

  /**
   * Generate unique selector for element
   */
  private generateSelector(element: HTMLElement, index: number): string {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.split(' ').filter((c) => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }

    return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
  }
}
