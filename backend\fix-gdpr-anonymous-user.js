/**
 * Fix anonymous user for GDPR scans
 * Simple JavaScript version to avoid TypeScript path issues
 */

const { Client } = require('pg');

const ANONYMOUS_USER_UUID = '00000000-0000-0000-0000-000000000000';

async function createAnonymousUser() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'complychecker_dev',
    user: 'complyuser',
    password: 'complypassword',
  });

  try {
    console.log('🔗 Connecting to database...');
    await client.connect();
    console.log('✅ Connected to database');

    // First, check the users table structure
    console.log('🔍 Checking users table structure...');
    const tableInfo = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'users'
      ORDER BY ordinal_position
    `);

    console.log('📋 Users table columns:');
    tableInfo.rows.forEach((row) => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // Check if anonymous user already exists
    console.log('🔍 Checking if anonymous user exists...');
    const existingUser = await client.query('SELECT id, email FROM users WHERE id = $1', [
      ANONYMOUS_USER_UUID,
    ]);

    if (existingUser.rows.length > 0) {
      console.log('✅ Anonymous user already exists');
      console.log(`   UUID: ${ANONYMOUS_USER_UUID}`);
      console.log(`   Email: ${existingUser.rows[0].email}`);
      console.log('   GDPR scans should work without foreign key errors');
      return;
    }

    // Create anonymous user
    console.log('📝 Creating anonymous user...');
    await client.query(
      `
      INSERT INTO users (
        id,
        keycloak_id,
        email,
        created_at,
        updated_at
      ) VALUES (
        $1,
        'anonymous-user-keycloak-id',
        '<EMAIL>',
        NOW(),
        NOW()
      )
    `,
      [ANONYMOUS_USER_UUID],
    );

    console.log('✅ Anonymous user created successfully!');
    console.log(`   UUID: ${ANONYMOUS_USER_UUID}`);
    console.log(`   Email: <EMAIL>`);
    console.log('   GDPR scans will now work without foreign key errors');
  } catch (error) {
    console.error('❌ Error creating anonymous user:', error);

    if (error.message.includes('connect')) {
      console.error('💡 Database connection failed. Make sure PostgreSQL is running.');
    } else if (error.message.includes('does not exist')) {
      console.error('💡 Database or table does not exist. Run migrations first.');
    } else if (error.code === '23505') {
      console.error('💡 User already exists (duplicate key error)');
    }

    throw error;
  } finally {
    try {
      await client.end();
      console.log('🔌 Database connection closed');
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }
  }
}

// Run the function
createAnonymousUser()
  .then(() => {
    console.log('🎉 Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
