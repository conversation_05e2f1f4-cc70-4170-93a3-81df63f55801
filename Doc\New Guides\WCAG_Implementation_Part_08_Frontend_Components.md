# WCAG Implementation Part 08: Frontend Components & Dashboard

## Overview

This document implements the React frontend components for WCAG compliance scanning, including the dashboard, scan results display, and management interfaces. All components follow established UI patterns and provide excellent user experience.

## ⚠️ CRITICAL REQUIREMENTS

### 🚫 NO ANY[] TYPES
**STRICTLY PROHIBITED**: All React components must use strict TypeScript typing.

### ✅ DEPENDENCIES
- **Parts 01-07 Complete**: Backend API and orchestrator ready
- **React Best Practices**: Proper component structure and state management
- **Accessibility**: Components must be accessible themselves
- **Responsive Design**: Mobile-first responsive layouts

## Prerequisites

- Parts 01-07 completed successfully
- React application with TypeScript configured
- UI component library (e.g., Material-UI, Ant Design) available
- API client for backend communication

## Step 1: Frontend Type Definitions

### 1.1 Create Frontend Types

Create `frontend/types/wcag.ts`:

```typescript
/**
 * Frontend WCAG Type Definitions
 * Client-side types for WCAG functionality
 */

// Re-export backend types for frontend use
export type {
  WcagScanConfig,
  WcagScanOptions,
  WcagScanResult,
  WcagScanSummary,
  WcagCheckResult,
  WcagEvidence,
  WcagRecommendation,
  WcagVersion,
  WcagLevel,
  WcagCategory,
  ScanStatus,
  CheckStatus,
  RiskLevel
} from '../../../backend/src/compliance/wcag/types';

// Frontend-specific types
export interface WcagScanFormData {
  targetUrl: string;
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;
}

export interface WcagDashboardState {
  currentScan?: WcagScanResult;
  recentScans: WcagScanResult[];
  isScanning: boolean;
  scanProgress: number;
  error?: string;
  selectedScanId?: string;
}

export interface WcagComponentProps {
  scanResult?: WcagScanResult;
  onScanStart?: (config: WcagScanConfig) => void;
  onExport?: (scanId: string, format: 'pdf' | 'json' | 'csv') => void;
  onRescan?: (scanId: string) => void;
  onDelete?: (scanId: string) => void;
}

export interface ScanProgressInfo {
  scanId: string;
  status: ScanStatus;
  currentCheck?: string;
  completedChecks: number;
  totalChecks: number;
  progress: number;
  estimatedTimeRemaining?: number;
}

export interface QueueStatusInfo {
  totalQueued: number;
  currentlyProcessing: number;
  maxConcurrent: number;
  averageWaitTime: number;
  estimatedWaitTime: number;
  position?: number;
}

// UI State types
export interface WcagUIState {
  activeTab: 'overview' | 'details' | 'recommendations' | 'history';
  selectedCategory?: WcagCategory;
  selectedLevel?: WcagLevel;
  showOnlyFailed: boolean;
  sortBy: 'ruleId' | 'score' | 'category' | 'level';
  sortOrder: 'asc' | 'desc';
  pageSize: number;
  currentPage: number;
}

// Chart data types
export interface ScoreChartData {
  category: string;
  score: number;
  maxScore: number;
  color: string;
}

export interface TrendChartData {
  date: string;
  score: number;
  level: string;
}

export interface ComplianceBreakdownData {
  level: WcagLevel;
  passed: number;
  failed: number;
  total: number;
  percentage: number;
}
```

## Step 2: API Service Layer

### 2.1 Create WCAG API Service

Create `frontend/services/wcag-api.ts`:

```typescript
/**
 * WCAG API Service
 * Frontend service for WCAG API communication
 */

import axios, { AxiosResponse } from 'axios';
import {
  WcagScanConfig,
  WcagScanResult,
  WcagScanFormData,
  ScanProgressInfo,
  QueueStatusInfo
} from '../types/wcag';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  requestId: string;
  processingTime: number;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
    context?: string;
  };
  requestId: string;
  processingTime: number;
}

export interface ScanListResponse {
  scans: Array<{
    scanId: string;
    targetUrl: string;
    status: string;
    overallScore?: number;
    levelAchieved?: string;
    riskLevel?: string;
    scanTimestamp: string;
    completionTimestamp?: string;
    totalAutomatedChecks?: number;
    passedAutomatedChecks?: number;
    failedAutomatedChecks?: number;
    manualReviewItems?: number; // Count only, no scoring
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class WcagApiService {
  private baseURL: string;
  private authToken: string | null = null;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_BASE_URL || '/api/v1';
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.authToken = token;
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = null;
    delete axios.defaults.headers.common['Authorization'];
  }

  /**
   * Start a new WCAG scan
   */
  async startScan(formData: WcagScanFormData): Promise<WcagScanResult> {
    try {
      const scanConfig: WcagScanConfig = {
        targetUrl: formData.targetUrl,
        scanOptions: {
          enableContrastAnalysis: formData.enableContrastAnalysis,
          enableKeyboardTesting: formData.enableKeyboardTesting,
          enableFocusAnalysis: formData.enableFocusAnalysis,
          enableSemanticValidation: formData.enableSemanticValidation,
          wcagVersion: formData.wcagVersion,
          level: formData.level,
          maxPages: formData.maxPages
        }
      };

      const response: AxiosResponse<ApiResponse<WcagScanResult>> = await axios.post(
        `${this.baseURL}/compliance/wcag/scan`,
        scanConfig
      );

      if (!response.data.success) {
        throw new Error('Scan request failed');
      }

      return response.data.data;
    } catch (error) {
      console.error('Failed to start WCAG scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get list of user's scans
   */
  async getScans(params: {
    page?: number;
    limit?: number;
    status?: string;
    sortBy?: string;
    sortOrder?: string;
  } = {}): Promise<ScanListResponse> {
    try {
      const response: AxiosResponse<ApiResponse<ScanListResponse>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans`,
        { params }
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch scans');
      }

      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch WCAG scans:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get detailed scan results
   */
  async getScanDetails(scanId: string): Promise<WcagScanResult> {
    try {
      const response: AxiosResponse<ApiResponse<WcagScanResult>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans/${scanId}`
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch scan details');
      }

      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch scan details:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Delete a scan
   */
  async deleteScan(scanId: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<{ message: string }>> = await axios.delete(
        `${this.baseURL}/compliance/wcag/scans/${scanId}`
      );

      if (!response.data.success) {
        throw new Error('Failed to delete scan');
      }
    } catch (error) {
      console.error('Failed to delete scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Export scan results
   */
  async exportScan(
    scanId: string,
    format: 'pdf' | 'json' | 'csv',
    options: {
      includeEvidence?: boolean;
      includeRecommendations?: boolean;
      includeManualReviewItems?: boolean; // Include manual review tracking
    } = {}
  ): Promise<Blob> {
    try {
      const response = await axios.post(
        `${this.baseURL}/compliance/wcag/export`,
        {
          scanId,
          format,
          ...options
        },
        {
          responseType: 'blob'
        }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to export scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get scan progress (for real-time updates)
   */
  async getScanProgress(scanId: string): Promise<ScanProgressInfo | null> {
    try {
      // This would typically use WebSocket or Server-Sent Events
      // For now, implement as polling endpoint
      const response: AxiosResponse<ApiResponse<ScanProgressInfo | null>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans/${scanId}/progress`
      );

      if (!response.data.success) {
        return null;
      }

      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch scan progress:', error);
      return null;
    }
  }

  /**
   * Get queue status
   */
  async getQueueStatus(): Promise<QueueStatusInfo> {
    try {
      const response: AxiosResponse<ApiResponse<QueueStatusInfo>> = await axios.get(
        `${this.baseURL}/compliance/wcag/queue/status`
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch queue status');
      }

      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch queue status:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Cancel a scan
   */
  async cancelScan(scanId: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<{ message: string }>> = await axios.post(
        `${this.baseURL}/compliance/wcag/scans/${scanId}/cancel`
      );

      if (!response.data.success) {
        throw new Error('Failed to cancel scan');
      }
    } catch (error) {
      console.error('Failed to cancel scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Check API health
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/compliance/wcag/health`);
      return response.data.success;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Handle API errors consistently
   */
  private handleApiError(error: any): Error {
    if (axios.isAxiosError(error)) {
      if (error.response?.data?.error) {
        const apiError = error.response.data as ApiError;
        return new Error(apiError.error.message || 'API request failed');
      }
      
      if (error.response?.status === 401) {
        return new Error('Authentication required - please log in');
      }
      
      if (error.response?.status === 403) {
        return new Error('Insufficient permissions for WCAG scanning');
      }
      
      if (error.response?.status === 429) {
        return new Error('Rate limit exceeded - please wait before making more requests');
      }
      
      return new Error(error.message || 'Network error occurred');
    }
    
    return error instanceof Error ? error : new Error('Unknown error occurred');
  }
}

// Export singleton instance
export const wcagApiService = new WcagApiService();
export default wcagApiService;
```

## Step 3: Core React Components

### 3.1 Create WCAG Scan Form Component

Create `frontend/components/wcag/WcagScanForm.tsx`:

```typescript
/**
 * WCAG Scan Form Component
 * Form for initiating new WCAG compliance scans
 */

import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  TextField,
  FormControlLabel,
  Checkbox,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Slider,
  Grid
} from '@mui/material';
import { PlayArrow, Settings } from '@mui/icons-material';
import { WcagScanFormData, WcagVersion, WcagLevel } from '../../types/wcag';

interface WcagScanFormProps {
  onSubmit: (formData: WcagScanFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
  disabled?: boolean;
}

const WcagScanForm: React.FC<WcagScanFormProps> = ({
  onSubmit,
  isLoading = false,
  error,
  disabled = false
}) => {
  const [formData, setFormData] = useState<WcagScanFormData>({
    targetUrl: '',
    enableContrastAnalysis: true,
    enableKeyboardTesting: true,
    enableFocusAnalysis: true,
    enableSemanticValidation: true,
    wcagVersion: 'all',
    level: 'AA',
    maxPages: 5
  });

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [urlError, setUrlError] = useState<string>('');

  /**
   * Validate URL format
   */
  const validateUrl = useCallback((url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }, []);

  /**
   * Handle form field changes
   */
  const handleChange = useCallback((field: keyof WcagScanFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Validate URL on change
    if (field === 'targetUrl') {
      if (value && !validateUrl(value)) {
        setUrlError('Please enter a valid URL (including http:// or https://)');
      } else {
        setUrlError('');
      }
    }
  }, [validateUrl]);

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();

    // Final validation
    if (!formData.targetUrl) {
      setUrlError('URL is required');
      return;
    }

    if (!validateUrl(formData.targetUrl)) {
      setUrlError('Please enter a valid URL');
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  }, [formData, onSubmit, validateUrl]);

  /**
   * Reset form to defaults
   */
  const handleReset = useCallback(() => {
    setFormData({
      targetUrl: '',
      enableContrastAnalysis: true,
      enableKeyboardTesting: true,
      enableFocusAnalysis: true,
      enableSemanticValidation: true,
      wcagVersion: 'all',
      level: 'AA',
      maxPages: 5
    });
    setUrlError('');
  }, []);

  return (
    <Card>
      <CardHeader
        title="Start WCAG Compliance Scan"
        subheader="Analyze your website for WCAG 2.1, 2.2, and 3.0 compliance"
        avatar={<PlayArrow color="primary" />}
      />
      <CardContent>
        <Box component="form" onSubmit={handleSubmit} noValidate>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Target URL */}
          <TextField
            fullWidth
            label="Website URL"
            placeholder="https://example.com"
            value={formData.targetUrl}
            onChange={(e) => handleChange('targetUrl', e.target.value)}
            error={!!urlError}
            helperText={urlError || 'Enter the URL of the website to scan'}
            disabled={disabled || isLoading}
            required
            sx={{ mb: 3 }}
          />

          {/* WCAG Version Selection */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>WCAG Version</InputLabel>
                <Select
                  value={formData.wcagVersion}
                  label="WCAG Version"
                  onChange={(e) => handleChange('wcagVersion', e.target.value as WcagVersion | 'all')}
                  disabled={disabled || isLoading}
                >
                  <MenuItem value="all">All Versions (2.1, 2.2, 3.0)</MenuItem>
                  <MenuItem value="2.1">WCAG 2.1</MenuItem>
                  <MenuItem value="2.2">WCAG 2.2</MenuItem>
                  <MenuItem value="3.0">WCAG 3.0 (Draft)</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Compliance Level</InputLabel>
                <Select
                  value={formData.level}
                  label="Compliance Level"
                  onChange={(e) => handleChange('level', e.target.value as WcagLevel)}
                  disabled={disabled || isLoading}
                >
                  <MenuItem value="A">Level A (Minimum)</MenuItem>
                  <MenuItem value="AA">Level AA (Standard)</MenuItem>
                  <MenuItem value="AAA">Level AAA (Enhanced)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>

          {/* Scan Options */}
          <Typography variant="h6" gutterBottom>
            Scan Options
          </Typography>
          
          <Grid container spacing={1} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableContrastAnalysis}
                    onChange={(e) => handleChange('enableContrastAnalysis', e.target.checked)}
                    disabled={disabled || isLoading}
                  />
                }
                label="Color Contrast Analysis"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableKeyboardTesting}
                    onChange={(e) => handleChange('enableKeyboardTesting', e.target.checked)}
                    disabled={disabled || isLoading}
                  />
                }
                label="Keyboard Accessibility Testing"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableFocusAnalysis}
                    onChange={(e) => handleChange('enableFocusAnalysis', e.target.checked)}
                    disabled={disabled || isLoading}
                  />
                }
                label="Focus Management Analysis"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.enableSemanticValidation}
                    onChange={(e) => handleChange('enableSemanticValidation', e.target.checked)}
                    disabled={disabled || isLoading}
                  />
                }
                label="Semantic Structure Validation"
              />
            </Grid>
          </Grid>

          {/* Advanced Options */}
          <Box sx={{ mb: 3 }}>
            <Button
              startIcon={<Settings />}
              onClick={() => setShowAdvanced(!showAdvanced)}
              disabled={disabled || isLoading}
              sx={{ mb: 2 }}
            >
              {showAdvanced ? 'Hide' : 'Show'} Advanced Options
            </Button>

            {showAdvanced && (
              <Box sx={{ pl: 2, borderLeft: 2, borderColor: 'divider' }}>
                <Typography gutterBottom>
                  Maximum Pages to Scan: {formData.maxPages}
                </Typography>
                <Slider
                  value={formData.maxPages}
                  onChange={(_, value) => handleChange('maxPages', value as number)}
                  min={1}
                  max={10}
                  marks
                  valueLabelDisplay="auto"
                  disabled={disabled || isLoading}
                  sx={{ mb: 2 }}
                />
                <Typography variant="caption" color="text.secondary">
                  Scanning more pages provides comprehensive results but takes longer
                </Typography>
              </Box>
            )}
          </Box>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              type="button"
              variant="outlined"
              onClick={handleReset}
              disabled={disabled || isLoading}
            >
              Reset
            </Button>
            
            <Button
              type="submit"
              variant="contained"
              disabled={disabled || isLoading || !!urlError || !formData.targetUrl}
              startIcon={isLoading ? <CircularProgress size={20} /> : <PlayArrow />}
            >
              {isLoading ? 'Starting Scan...' : 'Start Scan'}
            </Button>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default WcagScanForm;
```

### 3.2 Create Scan Results Overview Component

Create `frontend/components/wcag/WcagScanOverview.tsx`:

```typescript
/**
 * WCAG Scan Results Overview Component
 * Displays high-level scan results and metrics
 */

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  Alert,
  Divider
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Assessment,
  Schedule,
  Speed
} from '@mui/icons-material';
import { WcagScanResult, RiskLevel, WcagLevel } from '../../types/wcag';

interface WcagScanOverviewProps {
  scanResult: WcagScanResult;
}

const WcagScanOverview: React.FC<WcagScanOverviewProps> = ({ scanResult }) => {
  /**
   * Get risk level color
   */
  const getRiskColor = (riskLevel: RiskLevel): 'success' | 'warning' | 'error' | 'default' => {
    switch (riskLevel) {
      case 'low': return 'success';
      case 'medium': return 'warning';
      case 'high': return 'error';
      case 'critical': return 'error';
      default: return 'default';
    }
  };

  /**
   * Get level achievement color
   */
  const getLevelColor = (level: WcagLevel | 'FAIL'): 'success' | 'warning' | 'error' | 'default' => {
    switch (level) {
      case 'AAA': return 'success';
      case 'AA': return 'success';
      case 'A': return 'warning';
      case 'FAIL': return 'error';
      default: return 'default';
    }
  };

  /**
   * Format duration
   */
  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  /**
   * Calculate automation percentage
   */
  const automationPercentage = Math.round(scanResult.summary.automationRate * 100);

  return (
    <Box>
      {/* Status Alert */}
      {scanResult.status === 'completed' && (
        <Alert
          severity={scanResult.riskLevel === 'low' ? 'success' : scanResult.riskLevel === 'medium' ? 'warning' : 'error'}
          sx={{ mb: 3 }}
        >
          <Typography variant="h6" component="div">
            Scan Completed - {scanResult.levelAchieved} Level Achieved
          </Typography>
          <Typography variant="body2">
            Overall Score: {scanResult.overallScore}/100 | Risk Level: {scanResult.riskLevel.toUpperCase()}
          </Typography>
        </Alert>
      )}

      {scanResult.status === 'failed' && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6" component="div">
            Scan Failed
          </Typography>
          <Typography variant="body2">
            The WCAG compliance scan could not be completed. Please try again.
          </Typography>
        </Alert>
      )}

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Overall Score */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Assessment color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" component="div" color="primary">
                {scanResult.overallScore}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overall Score
              </Typography>
              <LinearProgress
                variant="determinate"
                value={scanResult.overallScore}
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* Level Achieved */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircle color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Chip
                label={scanResult.levelAchieved}
                color={getLevelColor(scanResult.levelAchieved)}
                size="large"
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                WCAG Level Achieved
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Risk Level */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Warning color="warning" sx={{ fontSize: 40, mb: 1 }} />
              <Chip
                label={scanResult.riskLevel.toUpperCase()}
                color={getRiskColor(scanResult.riskLevel)}
                size="large"
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                Risk Level
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Automation Rate */}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Speed color="info" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h4" component="div" color="info.main">
                {automationPercentage}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Automated Analysis
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Detailed Metrics */}
      <Grid container spacing={3}>
        {/* Check Results Summary */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Check Results Summary
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Passed Checks</Typography>
                  <Typography variant="body2" color="success.main">
                    {scanResult.summary.passedChecks}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(scanResult.summary.passedChecks / scanResult.summary.totalChecks) * 100}
                  color="success"
                  sx={{ mb: 2 }}
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Failed Checks</Typography>
                  <Typography variant="body2" color="error.main">
                    {scanResult.summary.failedChecks}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(scanResult.summary.failedChecks / scanResult.summary.totalChecks) * 100}
                  color="error"
                  sx={{ mb: 2 }}
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Manual Review Items</Typography>
                  <Typography variant="body2" color="info.main">
                    {scanResult.manualReviewSummary.totalManualItems}
                  </Typography>
                </Box>
                <Typography variant="caption" color="text.secondary">
                  Estimated time: {scanResult.manualReviewSummary.estimatedReviewTime} minutes
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="body2" color="text.secondary">
                Total Checks: {scanResult.summary.totalChecks}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Category Scores */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Category Scores
              </Typography>

              {Object.entries(scanResult.summary.categoryScores).map(([category, score]) => (
                <Box key={category} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                      {category}
                    </Typography>
                    <Typography variant="body2">
                      {score}/100
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={score}
                    color={score >= 80 ? 'success' : score >= 60 ? 'warning' : 'error'}
                  />
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        {/* Scan Metadata */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Scan Information
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Target URL
                  </Typography>
                  <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                    {scanResult.targetUrl}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Scan Duration
                  </Typography>
                  <Typography variant="body2">
                    {scanResult.metadata.duration ? formatDuration(scanResult.metadata.duration) : 'N/A'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Scan Date
                  </Typography>
                  <Typography variant="body2">
                    {new Date(scanResult.metadata.startTime).toLocaleDateString()}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="body2" color="text.secondary">
                    Scan ID
                  </Typography>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
                    {scanResult.scanId}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default WcagScanOverview;
```

### 3.3 Create Scan Progress Component

Create `frontend/components/wcag/WcagScanProgress.tsx`:

```typescript
/**
 * WCAG Scan Progress Component
 * Real-time progress display for running scans
 */

import React, { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Button,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  Stop,
  CheckCircle,
  Schedule,
  Speed
} from '@mui/icons-material';
import { ScanProgressInfo, QueueStatusInfo } from '../../types/wcag';

interface WcagScanProgressProps {
  scanId: string;
  onCancel?: () => void;
  onComplete?: () => void;
  refreshInterval?: number;
}

const WcagScanProgress: React.FC<WcagScanProgressProps> = ({
  scanId,
  onCancel,
  onComplete,
  refreshInterval = 2000
}) => {
  const [progress, setProgress] = useState<ScanProgressInfo | null>(null);
  const [queueStatus, setQueueStatus] = useState<QueueStatusInfo | null>(null);
  const [error, setError] = useState<string>('');
  const [isPolling, setIsPolling] = useState(true);

  /**
   * Fetch progress data
   */
  const fetchProgress = async () => {
    try {
      // This would use the API service
      // const progressData = await wcagApiService.getScanProgress(scanId);
      // const queueData = await wcagApiService.getQueueStatus();

      // Placeholder implementation
      const mockProgress: ScanProgressInfo = {
        scanId,
        status: 'running',
        currentCheck: 'Contrast Analysis',
        completedChecks: 8,
        totalChecks: 21,
        progress: 38,
        estimatedTimeRemaining: 120
      };

      setProgress(mockProgress);

      // Check if scan completed
      if (mockProgress.status === 'completed' || mockProgress.status === 'failed') {
        setIsPolling(false);
        if (onComplete) {
          onComplete();
        }
      }
    } catch (err) {
      setError('Failed to fetch scan progress');
      console.error('Progress fetch error:', err);
    }
  };

  /**
   * Handle scan cancellation
   */
  const handleCancel = async () => {
    try {
      // await wcagApiService.cancelScan(scanId);
      setIsPolling(false);
      if (onCancel) {
        onCancel();
      }
    } catch (err) {
      setError('Failed to cancel scan');
      console.error('Cancel error:', err);
    }
  };

  /**
   * Format time remaining
   */
  const formatTimeRemaining = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  /**
   * Get status color
   */
  const getStatusColor = (status: string): 'default' | 'primary' | 'success' | 'error' | 'warning' => {
    switch (status) {
      case 'pending': return 'default';
      case 'running': return 'primary';
      case 'completed': return 'success';
      case 'failed': return 'error';
      case 'cancelled': return 'warning';
      default: return 'default';
    }
  };

  // Set up polling
  useEffect(() => {
    if (!isPolling) return;

    const interval = setInterval(fetchProgress, refreshInterval);

    // Initial fetch
    fetchProgress();

    return () => clearInterval(interval);
  }, [scanId, refreshInterval, isPolling]);

  if (error) {
    return (
      <Alert severity="error">
        {error}
      </Alert>
    );
  }

  if (!progress) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading scan progress...</Typography>
          <LinearProgress sx={{ mt: 2 }} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">
            WCAG Scan Progress
          </Typography>
          <Chip
            label={progress.status.toUpperCase()}
            color={getStatusColor(progress.status)}
            icon={progress.status === 'running' ? <PlayArrow /> : <CheckCircle />}
          />
        </Box>

        {/* Progress Bar */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">
              Progress: {progress.completedChecks}/{progress.totalChecks} checks
            </Typography>
            <Typography variant="body2">
              {progress.progress}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={progress.progress}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        {/* Current Status */}
        {progress.status === 'running' && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Currently Running:
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
              {progress.currentCheck || 'Processing...'}
            </Typography>
          </Box>
        )}

        {/* Time Remaining */}
        {progress.estimatedTimeRemaining && progress.status === 'running' && (
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Schedule sx={{ mr: 1, color: 'text.secondary' }} />
            <Typography variant="body2" color="text.secondary">
              Estimated time remaining: {formatTimeRemaining(progress.estimatedTimeRemaining)}
            </Typography>
          </Box>
        )}

        {/* Queue Information */}
        {queueStatus && progress.status === 'pending' && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              Your scan is queued. Position in queue: {queueStatus.totalQueued}
            </Typography>
            <Typography variant="body2">
              Estimated wait time: {formatTimeRemaining(queueStatus.estimatedWaitTime)}
            </Typography>
          </Alert>
        )}

        {/* Completed Checks List */}
        {progress.completedChecks > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Recent Completed Checks:
            </Typography>
            <List dense>
              {/* This would show actual completed checks */}
              <ListItem>
                <ListItemIcon>
                  <CheckCircle color="success" fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Color Contrast Analysis"
                  secondary="Completed successfully"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <CheckCircle color="success" fontSize="small" />
                </ListItemIcon>
                <ListItemText
                  primary="Focus Visibility Check"
                  secondary="Completed successfully"
                />
              </ListItem>
            </List>
          </Box>
        )}

        {/* Action Buttons */}
        {progress.status === 'running' && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              color="error"
              startIcon={<Stop />}
              onClick={handleCancel}
            >
              Cancel Scan
            </Button>
          </Box>
        )}

        {progress.status === 'completed' && (
          <Alert severity="success">
            <Typography variant="body2">
              WCAG scan completed successfully! View results below.
            </Typography>
          </Alert>
        )}

        {progress.status === 'failed' && (
          <Alert severity="error">
            <Typography variant="body2">
              WCAG scan failed. Please try again or contact support.
            </Typography>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default WcagScanProgress;
```

## Validation Checklist

- [ ] Frontend type definitions with strict TypeScript
- [ ] API service layer with proper error handling
- [ ] WCAG scan form with validation and options
- [ ] Scan results overview with key metrics
- [ ] Real-time scan progress component
- [ ] Responsive design for mobile devices
- [ ] Accessibility compliance in components
- [ ] Integration with backend API endpoints
- [ ] Error handling and user feedback
- [ ] Ready for dashboard integration

## Next Steps

Continue with **Part 09: Dashboard Integration & Navigation** to create the complete dashboard layout and navigation system.

---

*These frontend components provide a solid foundation for the WCAG compliance dashboard with excellent user experience and proper TypeScript typing throughout.*
