🚀 HIPAA Privacy scan initiated via API
📋 Request details: {
targetUrl: 'https://www.athenahealth.com/',
timeout: 300000,
maxRedirects: 5,
enableLevel1: true,
enableLevel2: true,
enableLevel3: true,
clientIP: '::1',
userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}
[SECURITY_AUDIT] {"timestamp":"2025-06-27T15:55:09.750Z","event":"hipaa_privacy_scan_started","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"/scan","method":"POST","details":{"targetUrl":"https://www.athenahealth.com/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1"}}
🔍 Starting HIPAA Privacy scan with options: {
timeout: 300000,
maxRedirects: 5,
userAgent: 'HIPAA-Privacy-Scanner/1.0',
includeSubdomains: false,
enableLevel1: true,
enableLevel2: true,
enableLevel3: true,
cacheResults: true,
generateReport: true
}
HipaaOrchestrator: Starting comprehensive scan for https://www.athenahealth.com/ (ID: hipaa-1751039709752-nlpjmfo5h)
HipaaOrchestrator: Executing 3 checks
HipaaOrchestrator: Executing batch 1 with 3 checks
HipaaOrchestrator: Starting check privacy-policy-presence
HipaaOrchestrator: Starting check hipaa-specific-content
🔍 [Multi-Page Analysis] Starting discovery of all privacy policy pages...
HipaaOrchestrator: Starting check contact-information
🔍 [Privacy Policy Detection] Starting link detection for base URL: https://www.athenahealth.com/
📄 [Privacy Policy Detection] HTML content length: 365720
🎯 [Privacy Policy Detection] Using patterns: [
'/privacy\\s*policy/i',
'/privacy\\s*notice/i',
'/hipaa\\s*notice/i',
'/notice\\s*of\\s*privacy\\s*practices/i',
'/privacy\\s*statement/i',
'/data\\s*privacy/i',
'/\\bprivacy\\b/i',
'/data\\s*protection/i',
'/cookie\\s*policy/i',
'/terms\\s*.*\\s*privacy/i',
'/legal\\s*notices?/i',
'/legal\\s*&?\\s*policy\\s*info/i',
'/plan\\s*disclosures?/i',
'/privacy\\s*center/i',
'/privacy\\s*hub/i',
'/program\\s*provisions/i',
'/member\\s*privacy/i',
'/patient\\s*privacy/i',
'/health\\s*information\\s*privacy/i',
'/protected\\s*health\\s*information/i',
'/phi\\s*policy/i',
'/compliance\\s*notices?/i',
'/regulatory\\s*notices?/i',
'/legal\\s*information/i',
'/terms\\s*of\\s*use/i',
'/website\\s*security/i',
'/fraud\\s*prevention/i',
'/non.?discrimination\\s*notice/i'
]
🎯 [Privacy Policy Detection] Found privacy policy link: {
url: 'https://athenahealth.com/privacy-rights',
linkText: 'Privacy Policy',
href: 'https://athenahealth.com/privacy-rights',
format: 'other',
position: 255652,
context: '"> <a data-link-type="footer" data-link-text="Privacy Policy" data-link-url="https://athenahealth.co...'
}
🎯 [Privacy Policy Detection] Found privacy policy link: {
  url: 'https://www.athenahealth.com/terms-and-conditions/terms-of-use',
  linkText: 'Terms of Use',
  href: 'https://www.athenahealth.com/terms-and-conditions/terms-of-use',
  format: 'other',
  position: 255888,
  context: 'er-links-sublink p1" href="https://athenahealth.com/privacy-rights"> Privacy Policy<a data-link-type...'
}
🎯 [Privacy Policy Detection] Found privacy policy link: {
url: 'https://athenahealth.com/privacy-rights',
linkText: 'Privacy Policy',
href: 'https://athenahealth.com/privacy-rights',
format: 'other',
position: 274544,
context: 's-serif;text-align:center"> <a data-link-type="footer" data-link-text="Privacy Policy" data-link-url...'
}
🎯 [Privacy Policy Detection] Found privacy policy link: {
url: 'https://www.athenahealth.com/terms-and-conditions/terms-of-use',
linkText: 'Terms of Use',
href: 'https://www.athenahealth.com/terms-and-conditions/terms-of-use',
format: 'other',
position: 274776,
context: 'footer-links-sublink" href="https://athenahealth.com/privacy-rights">Privacy Policy<a data-link-type...'
}
✅ [Privacy Policy Detection] Link detection complete: {
totalLinksScanned: 197,
privacyPolicyLinksFound: 4,
discoveredUrls: [
{
url: 'https://athenahealth.com/privacy-rights',
text: 'Privacy Policy'
},
{
url: 'https://www.athenahealth.com/terms-and-conditions/terms-of-use',
text: 'Terms of Use'
},
{
url: 'https://athenahealth.com/privacy-rights',
text: 'Privacy Policy'
},
{
url: 'https://www.athenahealth.com/terms-and-conditions/terms-of-use',
text: 'Terms of Use'
}
]
}
HipaaOrchestrator: Completed check privacy-policy-presence - PASSED
📋 [Multi-Page Analysis] Found 4 privacy policy URLs: [
'https://athenahealth.com/privacy-rights',
'https://www.athenahealth.com/terms-and-conditions/terms-of-use',
'https://www.athenahealth.com/terms-and-conditions',
'https://privacyportal.onetrust.com/webform/************************************/cb37657f-a291-4caa-bdaa-b13cbd18dbdc'
]
📄 [Multi-Page Analysis] Fetching content from: https://athenahealth.com/privacy-rights
📄 [Content Extraction] Starting text extraction from HTML
📊 [Content Extraction] Original HTML length: 314911
🧹 [Content Extraction] Found elements to remove: { totalElements: 947, scriptsAndStyles: 24 }
📝 [Content Extraction] Raw text content length: 28488
✅ [Content Extraction] Text extraction complete: {
originalHtmlLength: 314911,
rawTextLength: 28488,
cleanedTextLength: 28394,
compressionRatio: '91.0%',
wordCount: 4034,
lineCount: 1
}
HipaaOrchestrator: Completed check contact-information - FAILED
📄 [Content Extraction] Starting text extraction from HTML
📊 [Content Extraction] Original HTML length: 314911
🧹 [Content Extraction] Found elements to remove: { totalElements: 947, scriptsAndStyles: 24 }
📝 [Content Extraction] Raw text content length: 28488
✅ [Content Extraction] Text extraction complete: {
originalHtmlLength: 314911,
rawTextLength: 28488,
cleanedTextLength: 28394,
compressionRatio: '91.0%',
wordCount: 4034,
lineCount: 1
}
✅ [Multi-Page Analysis] Successfully extracted content from https://athenahealth.com/privacy-rights: 28394 characters
📄 [Multi-Page Analysis] Fetching content from: https://www.athenahealth.com/terms-and-conditions/terms-of-use
📄 [Content Extraction] Starting text extraction from HTML
📊 [Content Extraction] Original HTML length: 309961
🧹 [Content Extraction] Found elements to remove: { totalElements: 908, scriptsAndStyles: 22 }
📝 [Content Extraction] Raw text content length: 26829
✅ [Content Extraction] Text extraction complete: {
originalHtmlLength: 309961,
rawTextLength: 26829,
cleanedTextLength: 26734,
compressionRatio: '91.4%',
wordCount: 3898,
lineCount: 1
}
✅ [Multi-Page Analysis] Successfully extracted content from https://www.athenahealth.com/terms-and-conditions/terms-of-use: 26734 characters
📄 [Multi-Page Analysis] Fetching content from: https://www.athenahealth.com/terms-and-conditions
📄 [Content Extraction] Starting text extraction from HTML
📊 [Content Extraction] Original HTML length: 340911
🧹 [Content Extraction] Found elements to remove: { totalElements: 1059, scriptsAndStyles: 22 }
📝 [Content Extraction] Raw text content length: 42655
✅ [Content Extraction] Text extraction complete: {
originalHtmlLength: 340911,
rawTextLength: 42655,
cleanedTextLength: 42561,
compressionRatio: '87.5%',
wordCount: 6021,
lineCount: 1
}
✅ [Multi-Page Analysis] Successfully extracted content from https://www.athenahealth.com/terms-and-conditions: 42561 characters
📄 [Multi-Page Analysis] Fetching content from: https://privacyportal.onetrust.com/webform/************************************/cb37657f-a291-4caa-bdaa-b13cbd18dbdc
📄 [Content Extraction] Starting text extraction from HTML
📊 [Content Extraction] Original HTML length: 264604
🧹 [Content Extraction] Found elements to remove: { totalElements: 19, scriptsAndStyles: 7 }
📝 [Content Extraction] Raw text content length: 5
✅ [Content Extraction] Text extraction complete: {
originalHtmlLength: 264604,
rawTextLength: 5,
cleanedTextLength: 0,
compressionRatio: '100.0%',
wordCount: 1,
lineCount: 1
}
✅ [Multi-Page Analysis] Successfully extracted content from https://privacyportal.onetrust.com/webform/************************************/cb37657f-a291-4caa-bdaa-b13cbd18dbdc: 0 characters
🔗 [Content Combination] Combined 3 pages in priority order: [
'privacy_policy (28394 chars)',
'terms_of_use (26734 chars)',
'terms_of_use (42561 chars)'
]
🎯 [Multi-Page Analysis] Combined content from 3/4 pages
📊 [Multi-Page Analysis] Total combined content length: 98180 characters
🔍 [Level 1 Analysis] Starting pattern matching analysis
📊 [Level 1 Analysis] Content length: 98180
📝 [Level 1 Analysis] Content preview: Patient LoginathenaOne Login Solutions Solutions Who We Serve Who We Serve Resources Resources Company Company Request a Demo Request a Demo SolutionsOur PlatformathenaOneAn AI-powered, all-in-one sol...
📋 [Level 1 Analysis] Analyzing HIPAA-specific patterns...
✅ [Level 1 Analysis] HIPAA analysis complete: { hipaaMatches: 28, hipaaScore: 78 }
🎯 [Level 1 Analysis] Pattern matching analysis complete: {
analysisType: 'HIPAA-specific',
score: 78,
foundPatterns: 28,
totalPatterns: 9,
confidence: 95,
processingTime: '9ms',
matchDetails: [
{
requirement: 'Notice of Privacy Practices header',
text: 'Privacy Notice...',
position: 19652
},
{
requirement: 'Individual rights section',
text: 'your rights...',
position: 17593
},
{
requirement: 'Individual rights section',
text: 'your rights...',
position: 22848
},
{
requirement: 'Uses and disclosures section',
text: 'use and disclosure...',
position: 21702
},
{
requirement: 'Uses and disclosures section',
text: 'sharing of your personal information.To opt out of...',
position: 18314
}
]
}
🧠 [Level 2 Analysis] Starting NLP analysis with Compromise.js
📊 [Level 2 Analysis] Text length: 98180
📝 [Level 2 Analysis] Text preview: Patient LoginathenaOne Login Solutions Solutions Who We Serve Who We Serve Resources Resources Company Company Request a Demo Request a Demo SolutionsOur PlatformathenaOneAn AI-powered, all-in-one sol...
🔍 [Level 2 Analysis] Parsing text with Compromise.js...
✅ [Level 2 Analysis] Text parsed successfully
👥 [Level 2 Analysis] Extracting entities...
✅ [Level 2 Analysis] Entity extraction complete: { people: 6, organizations: 104, phoneNumbers: 8, emails: 0 }
🔒 [Level 2 Analysis] Finding privacy statements...
✅ [Level 2 Analysis] Privacy statement analysis complete: { statementsFound: 229, privacyScore: 71.26637554585153 }
⚖️ [Level 2 Analysis] Finding rights statements...
✅ [Level 2 Analysis] Rights statement analysis complete: { statementsFound: 30, rightsScore: 72.5 }
📋 [Level 2 Analysis] Generating findings...
✅ [Level 2 Analysis] Findings generation complete: {
findingsCount: 5,
findingTypes: [
'entity_found',
'entity_found',
'concept_identified',
'concept_identified',
'concept_identified'
]
}
📊 [Level 2 Analysis] Calculating compliance score...
🎯 [Level 2 Analysis] NLP analysis complete: {
score: 100,
confidence: 100,
processingTime: '1882ms',
entitiesSummary: { people: 6, organizations: 104, phoneNumbers: 8, emails: 0 },
statementsSummary: { privacyStatements: 229, rightsStatements: 30 },
findingsSummary: { total: 5, types: [ 'entity_found', 'concept_identified' ] }
}
🤖 [Level 3 Analysis] Starting AI analysis with LegalBERT
📊 [Level 3 Analysis] Text length: 98180
📝 [Level 3 Analysis] Text preview: Patient LoginathenaOne Login Solutions Solutions Who We Serve Who We Serve Resources Resources Company Company Request a Demo Request a Demo SolutionsOur PlatformathenaOneAn AI-powered, all-in-one sol...
🔧 [Level 3 Analysis] Initializing AI models...
✅ [Level 3 Analysis] AI models initialized successfully
🔍 [Level 3 Analysis] Performing legal compliance assessment...
🧠 [Level 3 Analysis] Using holistic analysis approach for better accuracy...
🔍 [Holistic Analysis] Analyzing full text for comprehensive HIPAA compliance...
📊 [Holistic Analysis] Analysis details: {
totalScore: 98,
maxPossibleScore: 205,
baseScore: 48,
finalScore: 53,
criticalCoverage: '33.3%',
matchedPatterns: 10,
criticalMatches: 4,
contentLength: 98180
}
✅ [Level 3 Analysis] Holistic analysis complete: {
score: 53,
confidence: 92,
patternsFound: 10,
analysisMethod: 'holistic_full_text'
}
✅ [Level 3 Analysis] Legal compliance assessment complete: { score: 53, confidence: 92 }
🔍 [Level 3 Analysis] Finding compliance gaps...
✅ [Level 3 Analysis] Gap analysis complete: { gapsFound: 0, criticalGaps: 0, highGaps: 0 }
🔍 [Level 3 Analysis] Assessing privacy risks...
✅ [Level 3 Analysis] Risk assessment complete: { risksFound: 3, highRisks: 2 }
💡 [Level 3 Analysis] Generating AI recommendations...
✅ [Level 3 Analysis] Recommendations generated: 4
🎯 [Level 3 Analysis] Detecting positive findings...
✅ [Level 3 Analysis] Positive findings detected: 1
📋 [Level 3 Analysis] Generating findings...
✅ [Level 3 Analysis] Findings generated: 3
📊 [Level 3 Analysis] Calculating overall compliance score...
📊 [Level 3 Analysis] Calculating confidence score...
🎯 [Level 3 Analysis] AI analysis complete: {
score: 43,
confidence: 95,
processingTime: '32ms',
gapsSummary: { total: 0, critical: 0, high: 0, medium: 0 },
risksSummary: { total: 3, high: 2, types: [ 'privacy', 'security' ] },
recommendationsSummary: { total: 4, highPriority: 2 },
findingsSummary: { total: 3, types: [ 'risk_factor' ] }
}
🔢 [Score Calculation] Level combination details: {
level1Score: 78,
level1Weight: 0.3,
level1Contribution: 23.4,
level2Score: 100,
level2Weight: 0.35,
level2Contribution: 35,
level3Score: 43,
level3Weight: 0.35,
level3Contribution: 15.049999999999999,
totalScore: 73.45,
totalWeight: 0.9999999999999999,
finalScore: 73
}
HipaaOrchestrator: Completed check hipaa-specific-content - PASSED
📊 [Frontend Result Generation] Calculating summary statistics...
🏥 [Industry Detection] URL: https://www.athenahealth.com/ -> healthcare
🔢 [Improved Scoring] Calculation details: {
industryType: 'healthcare',
thresholds: { passing: 75, good: 85, excellent: 95, critical: 40 },
confidenceWeighting: false,
calculations: {
totalWeightedScore: 63.099999999999994,
totalWeight: 1,
totalConfidence: 207,
confidenceCount: 3,
averageConfidence: 69
}
}
✅ [Frontend Result Generation] Improved scoring applied: {
industryType: 'healthcare',
oldScoringWouldBe: 74,
newOverallScore: 63,
overallPassed: false,
complianceLevel: 'partially_compliant',
totalChecks: 3,
passedChecks: 2,
failedChecks: 1,
scoringBreakdown: [
{ checkId: 'HIPAA-PP-001', weight: 0.15, contribution: '12.0' },
{
checkId: 'HIPAA-COMPREHENSIVE-001',
weight: 0.7,
contribution: '51.1'
},
{ checkId: 'HIPAA-CONTACT-001', weight: 0.15, contribution: '0.0' }
]
}
🔍 [Frontend Result Generation] Extracting analysis levels...
✅ [Frontend Result Generation] Analysis levels extracted: [ 1, 2, 3 ]
💡 [Frontend Result Generation] Generating recommendations...
✅ [Frontend Result Generation] Recommendations generated: {
count: 5,
priorities: [
{ title: 'Mitigate Privacy Risk', priority: 2 },
{ title: 'Mitigate Security Risk', priority: 2 },
{
title: 'Address Privacy Officer Contact Information Issues',
priority: 2
},
{ title: 'Review and Update Privacy Policy', priority: 3 },
{ title: 'Mitigate Privacy Risk', priority: 4 }
]
}
✅ [Frontend Result Generation] Compliance level from improved scoring: partially_compliant
🎯 [Frontend Result Generation] Enhanced HIPAA scan completed with 63% compliance score
🎯 [Frontend Result Generation] Final enhanced HIPAA result generated: {
scanId: 'hipaa-1751039709752-nlpjmfo5h',
targetUrl: 'https://www.athenahealth.com/',
overallScore: 63,
overallPassed: false,
complianceLevel: 'partially_compliant',
processingTime: '12052ms',
checksExecuted: 3,
analysisLevelsUsed: [ 1, 2, 3 ],
recommendationsGenerated: 5,
issuesSummary: { critical: 0, high: 1, medium: 0, low: 1 },
levelBreakdown: [
{
checkId: 'HIPAA-PP-001',
name: 'Privacy Policy Presence',
passed: true,
score: undefined,
levelsUsed: [Array]
},
{
checkId: 'HIPAA-COMPREHENSIVE-001',
name: 'Comprehensive HIPAA Privacy Policy Analysis',
passed: true,
score: 73,
levelsUsed: [Array]
},
{
checkId: 'HIPAA-CONTACT-001',
name: 'Privacy Officer Contact Information',
passed: false,
score: undefined,
levelsUsed: [Array]
}
]
}
🎯 [Frontend Result Generation] Enhanced HIPAA scan hipaa-1751039709752-nlpjmfo5h completed in 12052ms
✅ HIPAA Privacy scan completed successfully
📊 Scan results summary: {
overallScore: 63,
overallPassed: false,
totalChecks: 3,
recommendations: 5
}
✅ Scan record created in scans table with ID: e3fe562e-1ece-4c96-ab66-774564fd9d34
💾 [Database Storage] Starting enhanced HIPAA results storage
📊 [Database Storage] Scan details: {
scanId: 'e3fe562e-1ece-4c96-ab66-774564fd9d34',
targetUrl: 'https://www.athenahealth.com/',
overallScore: 63,
overallPassed: false,
checksCount: 3,
recommendationsCount: 5,
processingTime: '12052ms'
}
🔄 [Database Storage] Starting database transaction...
📝 [Database Storage] Inserting main HIPAA scan record...
✅ [Database Storage] Main scan record saved with ID: 5ecc97c3-5071-42e9-9e98-6cfa35de5017
📋 [Database Storage] Saving check results...
📝 [Database Storage] Saving check 1/3: Privacy Policy Presence
📝 [Database Storage] Saving check 2/3: Comprehensive HIPAA Privacy Policy Analysis
❌ [Database Storage] Error saving enhanced HIPAA results: error: insert into "hipaa_analysis_levels" ("check_result_id", "confidence", "found_patterns", "level", "level_findings", "method", "processing_time_ms", "score", "total_patterns") values ($1, $2, $3, $4, $5, $6, $7, $8, $9) - column "found_patterns" of relation "hipaa_analysis_levels" does not exist
at Parser.parseErrorMessage (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\parser.ts:369:69)
at Parser.handlePacket (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\parser.ts:187:21)
at Parser.parse (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\parser.ts:102:30)
at Socket.<anonymous> (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\index.ts:7:48)
at Socket.emit (node:events:519:28)
at Socket.emit (node:domain:488:12)
at addChunk (node:internal/streams/readable:559:12)
at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
at Socket.Readable.push (node:internal/streams/readable:390:5)
at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
length: 148,
severity: 'ERROR',
code: '42703',
detail: undefined,
hint: undefined,
position: '71',
internalPosition: undefined,
internalQuery: undefined,
where: undefined,
schema: undefined,
table: undefined,
column: undefined,
dataType: undefined,
constraint: undefined,
file: 'parse_target.c',
line: '1066',
routine: 'checkInsertTargets'
}
⚠️ Failed to save privacy scan results to database: error: insert into "hipaa_analysis_levels" ("check_result_id", "confidence", "found_patterns", "level", "level_findings", "method", "processing_time_ms", "score", "total_patterns") values ($1, $2, $3, $4, $5, $6, $7, $8, $9) - column "found_patterns" of relation "hipaa_analysis_levels" does not exist
at Parser.parseErrorMessage (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\parser.ts:369:69)
at Parser.handlePacket (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\parser.ts:187:21)
at Parser.parse (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\parser.ts:102:30)
at Socket.<anonymous> (D:\Web projects\Comply Checker\node_modules\pg-protocol\src\index.ts:7:48)
at Socket.emit (node:events:519:28)
at Socket.emit (node:domain:488:12)
at addChunk (node:internal/streams/readable:559:12)
at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
at Socket.Readable.push (node:internal/streams/readable:390:5)
at TCP.onStreamRead (node:internal/stream_base_commons:191:23) {
length: 148,
severity: 'ERROR',
code: '42703',
detail: undefined,
hint: undefined,
position: '71',
internalPosition: undefined,
internalQuery: undefined,
where: undefined,
schema: undefined,
table: undefined,
column: undefined,
dataType: undefined,
constraint: undefined,
file: 'parse_target.c',
line: '1066',
routine: 'checkInsertTargets'
}
[SECURITY_AUDIT] {"timestamp":"2025-06-27T15:55:22.011Z","event":"hipaa_privacy_scan_completed","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"/scan","method":"POST","details":{"targetUrl":"https://www.athenahealth.com/","overallScore":63,"overallPassed":false,"scanDuration":12259}}
