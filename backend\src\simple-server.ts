/**
 * Simple server for testing HIPAA analysis without production dependencies
 * @file Simple Express server setup for development and testing
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { env } from '@lib/env';
import mainRouter from './routes';
import { sessionMiddleware } from './lib/keycloak';
import logger from './utils/logger';

const app = express();
const port = env.BACKEND_PORT;

// Basic middleware
app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Session middleware
app.use(sessionMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    environment: env.NODE_ENV,
  });
});

// API Routes
app.use('/api/v1', mainRouter);

// Root route
app.get('/', (req, res) => {
  res.json({
    message: 'Comply Checker Backend is alive!',
    version: '1.0.0',
    environment: env.NODE_ENV,
    uptime: process.uptime(),
  });
});

// Error handling
app.use((err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logger.error('Error', { error: err });
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(port, () => {
  logger.info(`✅ Server running on http://localhost:${port}`);
  logger.info(`📋 Health check: http://localhost:${port}/health`);
  logger.info(`🧪 Test HIPAA: http://localhost:${port}/api/v1/compliance/scan`);
});

export default app;
