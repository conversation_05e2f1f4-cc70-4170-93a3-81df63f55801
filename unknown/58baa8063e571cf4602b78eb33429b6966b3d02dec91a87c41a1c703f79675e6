'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getKeycloakInstance, initOptions } from '../lib/keycloak';
import Keycloak from 'keycloak-js';
import { getAuthMe } from '../lib/api'; // Import getAuthMe

interface AuthContextType {
  keycloak: Keycloak | null;
  authenticated: boolean;
  login: () => void;
  logout: () => void;
  getToken: () => Promise<string | undefined>;
  profile: Keycloak.KeycloakProfile | null;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [keycloak, setKeycloak] = useState<Keycloak | null>(null);
  const [authenticated, setAuthenticated] = useState(false);
  const [profile, setProfile] = useState<Keycloak.KeycloakProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const kcInstance = getKeycloakInstance();

      // Only initialize if the 'authenticated' property is still undefined
      // This indicates that init() has not yet been successfully called on this instance.
      if (kcInstance && typeof kcInstance.authenticated === 'undefined') {
        setKeycloak(kcInstance); // Set instance to state for other functions to use
        // Debug: Keycloak instance before init
        // Debug: Attempting Keycloak initialization with options
        kcInstance
          .init(initOptions)
          .then((auth) => {
            console.log('🔐 Keycloak init resolved. Authenticated:', auth);
            console.log('🔐 Keycloak instance after init:', kcInstance);
            setAuthenticated(auth);
            console.log('🔐 User authentication status set to:', auth);
            if (auth) {
              console.log('🔐 User is authenticated, loading profile...');
              kcInstance
                .loadUserProfile()
                .then((loadedProfile) => {
                  console.log('🔐 User profile loaded:', loadedProfile);
                  setProfile(loadedProfile);
                  // Call getAuthMe to sync user with backend and get our DB profile
                  getAuthMe()
                    .then(() => {
                      // Debug: Backend user profile/sync successful
                    })
                    .catch(() => {
                      // Error: Failed to fetch/sync backend user profile
                    });
                })
                .catch(() => {
                  // Error: Failed to load Keycloak user profile
                });
            }
            setLoading(false);
          })
          .catch((error) => {
            console.error('❌ Keycloak init failed:', error);
            setLoading(false);
          });

        // Setup token refresh handling only once after attempting init
        kcInstance.onTokenExpired = () => {
          // Debug: Keycloak token expired. Attempting to refresh...
          kcInstance
            .updateToken(30) // 30 seconds minimum validity
            .then((refreshed) => {
              if (refreshed) {
                // Debug: Token refreshed successfully
              }
            })
            .catch(() => {
              // Error: Failed to refresh token
              kcInstance.logout(); // Or handle appropriately
            });
        };
      } else if (kcInstance && typeof kcInstance.authenticated !== 'undefined') {
        // Keycloak instance was already initialized (e.g., due to StrictMode re-render or HMR)
        // Just sync React state with the existing Keycloak instance state.
        // Debug: Keycloak already initialized. Syncing state.
        // Debug: Existing Keycloak instance state
        setKeycloak(kcInstance);
        setAuthenticated(kcInstance.authenticated);
        if (kcInstance.authenticated) {
          kcInstance
            .loadUserProfile()
            .then(setProfile)
            .catch(() => {
              // Error: Failed to load profile on re-sync
            });
        } else {
          setProfile(null); // Clear profile if not authenticated
        }
        setLoading(false);
      } // End of the new else if block
    } // End of if (typeof window !== 'undefined')
  }, []); // Dependency array for useEffect

  const login = () => {
    keycloak?.login();
  };

  const logout = () => {
    keycloak?.logout({ redirectUri: window.location.origin });
  };

  const getToken = async (): Promise<string | undefined> => {
    if (!keycloak || !authenticated) return undefined;
    try {
      await keycloak.updateToken(5); // Update if less than 5s validity
      return keycloak.token;
    } catch (_error) {
      // Error: Failed to refresh token during getToken
      logout(); // Or handle appropriately
      return undefined;
    }
  };

  if (loading) {
    // You can return a loading spinner or a minimal layout here
    return <div>Loading authentication status...</div>;
  }

  return (
    <AuthContext.Provider
      value={{ keycloak, authenticated, login, logout, getToken, profile, loading }}
    >
      {children}
    </AuthContext.Provider>
  );
};
