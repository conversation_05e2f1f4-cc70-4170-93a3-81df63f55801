// backend/src/config/security.ts

/**
 * Security configuration for production deployment
 * Implements comprehensive security measures for HIPAA compliance
 */

import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Type definitions for security
type SecurityEventDetails = Record<string, unknown> | string;
type ExpressResponse = Response;
type ExpressNext = NextFunction;

export interface SecurityConfig {
  // Rate limiting
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  rateLimitSkipSuccessfulRequests: boolean;

  // CORS settings
  corsOrigins: string[];
  corsCredentials: boolean;

  // Content Security Policy
  cspDirectives: Record<string, string[]>;

  // Request validation
  maxRequestSizeMB: number;
  maxUrlLength: number;
  allowedFileTypes: string[];

  // Session security
  sessionSecure: boolean;
  sessionHttpOnly: boolean;
  sessionSameSite: 'strict' | 'lax' | 'none';

  // API security
  requireApiKey: boolean;
  apiKeyHeader: string;

  // Audit logging
  enableAuditLogging: boolean;
  logSensitiveData: boolean;
}

/**
 * Production security configuration
 */
export const PRODUCTION_SECURITY: SecurityConfig = {
  // Strict rate limiting for production
  rateLimitWindowMs: 15 * 60 * 1000, // 15 minutes
  rateLimitMaxRequests: 100,
  rateLimitSkipSuccessfulRequests: false,

  // Production CORS settings
  corsOrigins: [
    process.env.FRONTEND_URL || 'https://comply-checker.com',
    'https://app.comply-checker.com',
  ],
  corsCredentials: true,

  // Strict CSP for production
  cspDirectives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'"],
    styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
    fontSrc: ["'self'", 'https://fonts.gstatic.com'],
    imgSrc: ["'self'", 'data:', 'https:'],
    connectSrc: ["'self'"],
    frameSrc: ["'none'"],
    objectSrc: ["'none'"],
    baseUri: ["'self'"],
    formAction: ["'self'"],
  },

  // Request validation
  maxRequestSizeMB: 10,
  maxUrlLength: 2048,
  allowedFileTypes: ['text/html', 'text/plain', 'application/json'],

  // Secure session settings
  sessionSecure: true,
  sessionHttpOnly: true,
  sessionSameSite: 'strict',

  // API security
  requireApiKey: true,
  apiKeyHeader: 'X-API-Key',

  // Comprehensive audit logging
  enableAuditLogging: true,
  logSensitiveData: false,
};

/**
 * Development security configuration
 */
export const DEVELOPMENT_SECURITY: SecurityConfig = {
  rateLimitWindowMs: 15 * 60 * 1000,
  rateLimitMaxRequests: 1000, // More lenient for development
  rateLimitSkipSuccessfulRequests: true,

  corsOrigins: ['http://localhost:3000', 'http://localhost:3001'],
  corsCredentials: true,

  cspDirectives: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", 'data:', 'https:', 'http:'],
    connectSrc: ["'self'", 'ws:', 'wss:'],
  },

  maxRequestSizeMB: 50, // More lenient for development
  maxUrlLength: 4096,
  allowedFileTypes: [
    'text/html',
    'text/plain',
    'application/json',
    'text/css',
    'application/javascript',
  ],

  sessionSecure: false, // HTTP allowed in development
  sessionHttpOnly: true,
  sessionSameSite: 'lax',

  requireApiKey: false, // Disabled for development
  apiKeyHeader: 'X-API-Key',

  enableAuditLogging: true,
  logSensitiveData: true, // Allowed in development
};

/**
 * Get security configuration based on environment
 */
export function getSecurityConfig(): SecurityConfig {
  const env = process.env.NODE_ENV || 'development';
  return env === 'production' ? PRODUCTION_SECURITY : DEVELOPMENT_SECURITY;
}

/**
 * Create rate limiter middleware
 */
export function createRateLimiter(config: SecurityConfig) {
  return rateLimit({
    windowMs: config.rateLimitWindowMs,
    max: config.rateLimitMaxRequests,
    skipSuccessfulRequests: config.rateLimitSkipSuccessfulRequests,
    message: {
      error: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(config.rateLimitWindowMs / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
}

/**
 * Create Helmet security middleware
 */
export function createHelmetConfig(config: SecurityConfig) {
  return helmet({
    contentSecurityPolicy: {
      directives: config.cspDirectives,
    },
    hsts: {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: true,
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true,
  });
}

/**
 * Input validation utilities
 */
export class InputValidator {
  static isValidUrl(
    url: string,
    options?: { allowedDomains?: string[]; blockedDomains?: string[] },
  ): boolean {
    const config = getSecurityConfig();
    try {
      const parsed = new URL(url);
      const isValidProtocol = parsed.protocol === 'http:' || parsed.protocol === 'https:';
      const isValidLength = url.length <= config.maxUrlLength;
      const isNotSuspicious = !this.containsSuspiciousPatterns(url);

      // Check domain restrictions if provided
      if (options?.allowedDomains && options.allowedDomains.length > 0) {
        const isDomainAllowed = options.allowedDomains.some((domain) =>
          parsed.hostname.includes(domain),
        );
        if (!isDomainAllowed) return false;
      }

      if (options?.blockedDomains && options.blockedDomains.length > 0) {
        const isDomainBlocked = options.blockedDomains.some((domain) =>
          parsed.hostname.includes(domain),
        );
        if (isDomainBlocked) return false;
      }

      return isValidLength && isValidProtocol && isNotSuspicious;
    } catch {
      return false;
    }
  }

  static containsSuspiciousPatterns(input: string): boolean {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i,
      /eval\(/i,
      /document\.cookie/i,
      /window\.location/i,
    ];

    return suspiciousPatterns.some((pattern) => pattern.test(input));
  }

  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/vbscript:/gi, '') // Remove vbscript: protocol
      .trim();
  }

  static validateContentType(contentType: string, config: SecurityConfig): boolean {
    return config.allowedFileTypes.some((allowed) =>
      contentType.toLowerCase().includes(allowed.toLowerCase()),
    );
  }
}

/**
 * Audit logging utilities
 */
export class AuditLogger {
  static logSecurityEvent(event: string, details: SecurityEventDetails, req?: Request): void {
    const config = getSecurityConfig();

    if (!config.enableAuditLogging) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      ip: req?.ip || 'unknown',
      userAgent: req?.get('User-Agent') || 'unknown',
      url: req?.url || 'unknown',
      method: req?.method || 'unknown',
      details: config.logSensitiveData ? details : this.sanitizeDetails(details),
    };

    logger.info('[SECURITY_AUDIT]', logEntry);
  }

  private static sanitizeDetails(details: SecurityEventDetails): SecurityEventDetails {
    if (typeof details !== 'object' || details === null) {
      return '[REDACTED]';
    }

    const sanitized: SecurityEventDetails = {};
    for (const [key, value] of Object.entries(details)) {
      if (this.isSensitiveField(key)) {
        sanitized[key] = '[REDACTED]';
      } else if (value === null) {
        sanitized[key] = null;
      } else if (typeof value === 'object') {
        sanitized[key] = this.sanitizeDetails(value as Record<string, unknown>);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  private static isSensitiveField(fieldName: string): boolean {
    const sensitiveFields = [
      'password',
      'token',
      'key',
      'secret',
      'auth',
      'session',
      'cookie',
      'email',
      'phone',
      'ssn',
      'credit',
    ];

    return sensitiveFields.some((sensitive) => fieldName.toLowerCase().includes(sensitive));
  }
}

/**
 * API key validation middleware
 */
export function validateApiKey(config: SecurityConfig) {
  return (req: Request, res: ExpressResponse, next: ExpressNext) => {
    if (!config.requireApiKey) {
      return next();
    }

    const apiKey = req.get(config.apiKeyHeader);
    const validApiKey = process.env.API_KEY;

    if (!apiKey || !validApiKey || apiKey !== validApiKey) {
      AuditLogger.logSecurityEvent(
        'INVALID_API_KEY',
        {
          providedKey: apiKey ? '[PROVIDED]' : '[MISSING]',
        },
        req,
      );

      return res.status(401).json({
        error: 'Invalid or missing API key',
        code: 'UNAUTHORIZED',
      });
    }

    next();
  };
}

export default {
  getSecurityConfig,
  createRateLimiter,
  createHelmetConfig,
  InputValidator,
  AuditLogger,
  validateApiKey,
  PRODUCTION_SECURITY,
  DEVELOPMENT_SECURITY,
};
