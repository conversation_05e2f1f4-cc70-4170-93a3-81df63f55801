import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';
import { TrackerDatabase } from '../utils/tracker-database';
import { GdprDatabase } from '../database/gdpr-database';

export interface TrackerDetectionCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId?: string;
}

export class TrackerDetectionCheck {
  /**
   * Detect third-party trackers and analyze consent compliance
   * REAL ANALYSIS - monitors actual network requests and scripts
   */
  async performCheck(config: TrackerDetectionCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      // Monitor network requests
      const networkRequests: Array<{
        url: string;
        domain: string;
        type: string;
        beforeConsent: boolean;
      }> = [];

      page.on('request', (request) => {
        const url = request.url();
        const domain = new URL(url).hostname;
        networkRequests.push({
          url,
          domain,
          type: request.resourceType(),
          beforeConsent: true, // Initially all requests are before consent
        });
      });

      // Navigate to website
      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      await new Promise((resolve) => setTimeout(resolve, 3000)); // Allow time for trackers to load

      // Analyze trackers found before consent
      const trackersBeforeConsent = this.analyzeTrackers(networkRequests);

      // Test consent interaction if banner exists
      const consentTestResult = await this.testConsentTrackerBlocking(page);

      // Calculate compliance score
      const totalTrackers = trackersBeforeConsent.length;
      const highRiskTrackers = trackersBeforeConsent.filter(
        (t) => t.classification.riskLevel === 'critical' || t.classification.riskLevel === 'high',
      ).length;

      if (totalTrackers === 0) {
        score = 100;
        evidence.push({
          type: 'network',
          description: 'No third-party trackers detected',
          value: 'No tracking concerns',
        });
      } else {
        // Penalty for high-risk trackers loading before consent
        const riskPenalty = highRiskTrackers * 20;
        score = Math.max(0, 100 - riskPenalty);

        evidence.push({
          type: 'network',
          description: 'Third-party trackers detected',
          value: `${totalTrackers} trackers found, ${highRiskTrackers} high-risk`,
        });

        // Add evidence for each tracker
        for (const tracker of trackersBeforeConsent.slice(0, 5)) {
          // Limit to top 5
          evidence.push({
            type: 'network',
            description: `Tracker: ${tracker.classification.name}`,
            value: `Domain: ${tracker.domain}, Risk: ${tracker.classification.riskLevel}`,
          });
        }
      }

      // Apply consent test results
      if (consentTestResult.tested) {
        if (consentTestResult.blockingWorking) {
          score += 20; // Bonus for working consent blocking
          evidence.push({
            type: 'network',
            description: 'Consent-based tracker blocking working',
            value: consentTestResult.evidence,
          });
        } else {
          evidence.push({
            type: 'network',
            description: 'Consent-based tracker blocking not working',
            value: consentTestResult.evidence,
          });
        }
      }

      // Save tracker analysis to database
      if (config.scanId) {
        await this.saveTrackerAnalysisToDatabase(config.scanId, trackersBeforeConsent);
      }

      const passed = score >= 70;

      return {
        ruleId: 'GDPR-006',
        ruleName: 'Third-Party Tracker Detection',
        category: 'cookies',
        passed,
        score: Math.min(100, score),
        weight: 6,
        severity: 'medium',
        evidence,
        recommendations: this.generateTrackerRecommendations(trackersBeforeConsent, score),
        manualReviewRequired: false,
      };
    } catch (error) {
      return {
        ruleId: 'GDPR-006',
        ruleName: 'Third-Party Tracker Detection',
        category: 'cookies',
        passed: false,
        score: 0,
        weight: 6,
        severity: 'medium',
        evidence: [
          {
            type: 'text',
            description: 'Tracker detection failed',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: 'Implement tracker consent management',
            description: 'Add consent-based blocking for third-party trackers',
            implementation: 'Configure consent management platform to block trackers',
            effort: 'significant',
            impact: 'high',
          },
        ],
        manualReviewRequired: false,
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze network requests for trackers
   */
  private analyzeTrackers(
    requests: Array<{
      url: string;
      domain: string;
      type: string;
      beforeConsent: boolean;
    }>,
  ): Array<{
    domain: string;
    url: string;
    classification: {
      category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
      name: string;
      dataTypes: string[];
      riskLevel: 'critical' | 'high' | 'medium' | 'low';
    };
    beforeConsent: boolean;
  }> {
    const trackers: Array<{
      domain: string;
      url: string;
      classification: {
        category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
        name: string;
        dataTypes: string[];
        riskLevel: 'critical' | 'high' | 'medium' | 'low';
      };
      beforeConsent: boolean;
    }> = [];

    const uniqueDomains = new Set<string>();

    for (const request of requests) {
      if (!uniqueDomains.has(request.domain)) {
        uniqueDomains.add(request.domain);

        const classification = TrackerDatabase.classifyTracker(request.domain);

        // Only include known trackers (not unknown)
        if (classification.category !== 'unknown') {
          trackers.push({
            domain: request.domain,
            url: request.url,
            classification,
            beforeConsent: request.beforeConsent,
          });
        }
      }
    }

    return trackers;
  }

  /**
   * Test consent-based tracker blocking
   */
  private async testConsentTrackerBlocking(page: Page): Promise<{
    tested: boolean;
    blockingWorking: boolean;
    evidence: string;
  }> {
    try {
      // Look for consent banner
      const bannerExists = await page.evaluate(() => {
        const selectors = [
          '[class*="cookie"]',
          '[class*="consent"]',
          '[id*="cookie"]',
          '[id*="consent"]',
        ];

        for (const selector of selectors) {
          const element = document.querySelector(selector);
          if (element) {
            const rect = element.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
          }
        }
        return false;
      });

      if (!bannerExists) {
        return {
          tested: false,
          blockingWorking: false,
          evidence: 'No consent banner found for testing',
        };
      }

      // Try to reject cookies/trackers
      const rejectionResult = await page.evaluate(() => {
        const rejectButtons = document.querySelectorAll('button, a, input');
        for (const button of rejectButtons) {
          const text = button.textContent?.toLowerCase() || '';
          if (text.includes('reject') || text.includes('decline') || text.includes('deny')) {
            (button as HTMLElement).click();
            return true;
          }
        }
        return false;
      });

      if (rejectionResult) {
        await new Promise((resolve) => setTimeout(resolve, 2000)); // Wait for changes

        // Check if tracking scripts are still active
        const trackingStillActive = await page.evaluate(() => {
          // Check for common tracking functions
          const trackingIndicators = [
            typeof (window as Window & { gtag?: (...args: unknown[]) => void }).gtag === 'function',
            typeof (window as Window & { ga?: (...args: unknown[]) => void }).ga === 'function',
            typeof (window as Window & { fbq?: (...args: unknown[]) => void }).fbq === 'function',
            document.cookie.includes('_ga'),
            document.cookie.includes('_fbp'),
          ];

          return trackingIndicators.some((indicator) => indicator);
        });

        return {
          tested: true,
          blockingWorking: !trackingStillActive,
          evidence: trackingStillActive
            ? 'Tracking still active after rejection'
            : 'Tracking properly blocked after rejection',
        };
      }

      return {
        tested: false,
        blockingWorking: false,
        evidence: 'Could not interact with consent controls',
      };
    } catch (error) {
      return {
        tested: false,
        blockingWorking: false,
        evidence: `Consent testing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Save tracker analysis to database
   */
  private async saveTrackerAnalysisToDatabase(
    scanId: string,
    trackers: Array<{
      domain: string;
      classification: {
        category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
        name: string;
        dataTypes: string[];
        riskLevel: 'critical' | 'high' | 'medium' | 'low';
      };
      beforeConsent: boolean;
    }>,
  ): Promise<void> {
    try {
      const trackerRecords = trackers.map((tracker) => ({
        domain: tracker.domain,
        name: tracker.classification.name,
        type: tracker.classification.category,
        loadsBeforeConsent: tracker.beforeConsent,
        hasConsentMechanism: false, // Would need deeper analysis
        dataTransferred: tracker.classification.dataTypes.join(', '),
        privacyPolicyMentioned: false, // Would need policy analysis
        compliant: !tracker.beforeConsent || tracker.classification.riskLevel === 'low',
      }));

      await GdprDatabase.saveTrackerAnalysis(scanId, trackerRecords);
    } catch (error) {
      console.error('Failed to save tracker analysis:', error);
    }
  }

  /**
   * Generate tracker-specific recommendations
   */
  private generateTrackerRecommendations(
    trackers: Array<{
      classification: {
        riskLevel: 'critical' | 'high' | 'medium' | 'low';
      };
    }>,
    score: number,
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    const highRiskTrackers = trackers.filter(
      (t) => t.classification.riskLevel === 'critical' || t.classification.riskLevel === 'high',
    );

    if (highRiskTrackers.length > 0) {
      recommendations.push({
        priority: 1,
        title: 'Implement consent for high-risk trackers',
        description: `${highRiskTrackers.length} high-risk trackers need consent management`,
        implementation: 'Configure consent management to block high-risk trackers before consent',
        effort: 'significant',
        impact: 'high',
      });
    }

    if (trackers.length > 0 && score < 70) {
      recommendations.push({
        priority: 2,
        title: 'Review all third-party integrations',
        description: 'Audit all tracking and analytics integrations for GDPR compliance',
        implementation: 'Document all trackers and implement proper consent mechanisms',
        effort: 'moderate',
        impact: 'high',
      });
    }

    return recommendations;
  }
}
