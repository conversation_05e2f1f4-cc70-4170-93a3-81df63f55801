Below is a comprehensive WCAG compliance report in **Markdown** format. Each Success Criterion (SC) is grouped by version (2.1, 2.2, 3.0 draft), with a concise “What to Check” list and a **Check Type** note indicating whether it can be (fully/partially) automated or requires manual review.

---

## WCAG 2.1 Success Criteria

### 1.1.1 Non‑text Content (WCAG 2.1)

**What to Check**

* Every meaningful image, icon, chart, etc., has a descriptive `alt` attribute or equivalent.  
* Decorative images are marked empty (`alt=""`) or hidden from assistive technology (`aria-hidden="true"`).  
* Complex visuals (charts, diagrams) have longer text descriptions or linked transcripts.

**Check Type:**

* **Automated**: detect missing/empty `alt` attributes  
* **Manual**: verify that provided text actually conveys the intended meaning

---

### 1.2.2 Captions (Prerecorded) (WCAG 2.1)

**What to Check**

* All prerecorded audio (dialogue, narration) in videos has accurately synchronized captions.  
* Captions include speaker IDs and non‑speech sounds (e.g., “[door creaks]”).  
* Caption files are selectable (e.g., `<track kind="captions">`) and toggleable.

**Check Type:**

* **Automated**: detect presence of caption tracks  
* **Manual**: confirm timing, accuracy, speaker labels, and non‑speech descriptions

---

### 1.3.1 Info & Relationships (WCAG 2.1)

**What to Check**

* Use semantic HTML: headings (`<h*>`), lists (`<ul>`, `<ol>`), and table headers (`<th>`).  
* Form controls paired with `<label>` elements.  
* ARIA roles and properties only when native semantics are insufficient.

**Check Type:**

* **Automated**: flag missing headings, list structures, or `<label>`s  
* **Manual**: ensure markup accurately reflects visual grouping and meaning

---

### 1.4.3 Contrast (Minimum) (WCAG 2.1)

**What to Check**

* Text and images of text meet at least 4.5:1 contrast (3:1 for large text ≥18 pt/14 pt bold).  
* Graphical objects (icons used as controls) meet the same contrast ratio.

**Check Type:**

* **Automated**: measure contrast ratios programmatically  
* **Manual**: verify on different backgrounds and in situ

---

### 2.1.1 Keyboard (WCAG 2.1)

**What to Check**

* All interactive elements (links, buttons, form controls, menus) are reachable and operable via Tab, Enter, Space, Arrow keys.  
* No keyboard traps: focus can move away from every control.

**Check Type:**

* **Automated**: partial (detect missing focusable elements)  
* **Manual**: full walkthrough using keyboard only

---

### 2.4.3 Focus Order (WCAG 2.1)

**What to Check**

* Tabbing through controls follows a logical sequence that matches visual layout and reading order.  
* Focusable elements not hidden off‑screen or excluded from tab order improperly.

**Check Type:**

* **Automated**: partial (verify DOM order vs. visual order)  
* **Manual**: navigate entire page with keyboard to ensure meaningful flow

---

### 2.4.7 Focus Visible (WCAG 2.1)

**What to Check**

* A clear, visible indicator (outline or background change) appears when an element receives focus.  
* Indicator is at least 2 px thick and contrasts at least 3:1 with adjacent colors.

**Check Type:**

* **Automated**: detect existence of `:focus` style rules with sufficient thickness/contrast  
* **Manual**: verify visibility against real page backgrounds

---

### 3.3.1 Error Identification (WCAG 2.1)

**What to Check**

* When form input errors occur, the error fields are identified in text (e.g., “Email is required”).  
* Error messages are programmatically associated (e.g., `aria-invalid="true"` and `aria-describedby`).  
* Suggestions for correction are provided.

**Check Type:**

* **Automated**: detect presence of `aria-invalid` and `aria-describedby` on invalid fields  
* **Manual**: test form submission with invalid inputs to review clarity and guidance

---

### 4.1.2 Name, Role, Value (WCAG 2.1)

**What to Check**

* Every UI component exposed to assistive tech has a programmatic name (`aria-label` or equivalent), role (`role`), and current value exposed (`aria-valuenow`, `value`).  
* Native HTML widgets inherently comply (e.g., `<button>`, `<input>`).

**Check Type:**

* **Automated**: audit ARIA attributes on custom controls  
* **Manual**: validate role/value/name accuracy in screen reader testing

---

## WCAG 2.2 New Success Criteria

### 2.4.11 Focus Not Obscured (Minimum) (WCAG 2.2)

**What to Check**

* When an element receives keyboard focus, it is not fully hidden by any fixed header/footer or overlay.  
* Page should scroll or adjust so focused element remains visible.

**Check Type:**

* **Automated**: detect CSS `scroll-padding` or `scroll-margin` usage  
* **Manual**: tab through page and ensure focus ring always visible

---

### 2.4.12 Focus Not Obscured (Enhanced) (WCAG 2.2)

**What to Check**

* Strict: Focused element is *never* hidden by author content under any circumstance.

**Check Type:**

* **Automated**: same as 2.4.11 with stricter rules  
* **Manual**: intensive keyboard walkthrough on all viewports

---

### 2.4.13 Focus Appearance (WCAG 2.2 AAA)

**What to Check**

* Focus indicator thickness ≥2 px and contrast ≥3:1 against the element’s unfocused state.

**Check Type:**

* **Automated**: measure style properties  
* **Manual**: visual confirmation on varied backgrounds

---

### 2.5.7 Dragging Movements (WCAG 2.2)

**What to Check**

* Any drag action (e.g., drag‑and‑drop, sliders) has a non‑drag alternative (e.g., arrow keys, tap).  
* Essential dragging (e.g., painting apps) must still provide a fallback.

**Check Type:**

* **Manual only**: review UI components and test alternative interactions

---

### 2.5.8 Target Size (Minimum) (WCAG 2.2)

**What to Check**

* Touch/click targets are at least 24 × 24 CSS px, unless spaced >16 CSS px from other targets.

**Check Type:**

* **Automated**: measure element bounding boxes and spacing  
* **Manual**: spot‑check on real devices

---

### 3.2.6 Consistent Help (WCAG 2.2)

**What to Check**

* Help/contact links appear in the same relative order on every page.  
* Self‑help, live chat, or email links consistently located.

**Check Type:**

* **Manual only**: cross‑page inspection

---

### 3.3.7 Redundant Entry (WCAG 2.2)

**What to Check**

* Data entered in one step (e.g., name/address) is carried forward or pre‑populated in later steps of the same process.

**Check Type:**

* **Automated**: detect `autocomplete` attributes  
* **Manual**: test multi‑step forms

---

### 3.3.8 Accessible Authentication (Minimum) (WCAG 2.2)

**What to Check**

* No memory‑based tests (like CAPTCHAs) unless a simpler alternative is offered (e.g., email link, SMS code).

**Check Type:**

* **Manual only**: review login flows and alternate mechanisms

---

### 3.3.9 Accessible Authentication (Enhanced) (WCAG 2.2 AAA)

**What to Check**

* Same as 3.3.8: ensure any test of memory/cognition has an alternative.

**Check Type:**

* **Manual only**: as above

---

## WCAG 3.0 (Silver – Draft Outcomes)

> **Note:** WCAG 3.0 is still a Working Draft. Outcomes below map roughly to 2.x criteria but are reorganized by user need. Always refer to the latest draft on W3C.

### 2.1 Image Alternatives (WCAG 3.0)

**What to Check**

* All meaningful images, controls, and charts have concise text alternatives.

**Check Type:**

* **Automated**: detect missing alt/text  
* **Manual**: evaluate for accuracy

---

### 2.2 Text and Wording (WCAG 3.0)

**What to Check**

* Content uses plain language; avoid jargon.  
* Reading level is appropriate or simplified versions are provided.

**Check Type:**

* **Manual only**: language review or readability tool plus human judgment

---

### 2.4 Keyboard Focus (WCAG 3.0)

**What to Check**

* Focusable elements visible, logical order, never obscured, consistent styling.

**Check Type:**

* **Automated**: partial (outline, order)  
* **Manual**: full keyboard navigation

---

### 2.5 Motor (WCAG 3.0)

**What to Check**

* All interface components operable without complex gestures; targets large enough.

**Check Type:**

* **Automated**: measure sizings  
* **Manual**: test gesture alternatives

---

### 3.1 Pronunciation & Meaning (WCAG 3.0)

**What to Check**

* Unusual words are defined; abbreviations expanded.

**Check Type:**

* **Manual only**: content review

---

*For a complete, versioned list of all WCAG 3.0 draft outcomes and their detailed techniques/failures, see the W3C Silver Draft at:*  
[https://www.w3.org/WAI/standards-guidelines/wcag/glance/#silver](https://www.w3.org/WAI/standards-guidelines/wcag/glance/#silver)
