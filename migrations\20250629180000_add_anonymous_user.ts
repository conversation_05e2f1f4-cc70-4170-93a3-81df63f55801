/**
 * Migration: Add Anonymous User for GDPR Scans
 * 
 * This migration creates a special anonymous user record to handle
 * GDPR scans from unauthenticated users, fixing foreign key constraint violations.
 */

import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create anonymous user record to satisfy foreign key constraints
  const ANONYMOUS_USER_UUID = '00000000-0000-0000-0000-000000000000';
  
  // Check if anonymous user already exists
  const existingUser = await knex('users')
    .where('id', ANONYMOUS_USER_UUID)
    .first();
  
  if (!existingUser) {
    console.log('Creating anonymous user for GDPR scans...');
    
    await knex('users').insert({
      id: ANONYMOUS_USER_UUID,
      keycloak_id: 'anonymous-user-keycloak-id',
      email: '<EMAIL>',
      created_at: new Date(),
      updated_at: new Date(),
    });
    
    console.log('✅ Anonymous user created successfully');
  } else {
    console.log('Anonymous user already exists, skipping...');
  }
}

export async function down(knex: Knex): Promise<void> {
  // Remove anonymous user
  const ANONYMOUS_USER_UUID = '00000000-0000-0000-0000-000000000000';
  
  console.log('Removing anonymous user...');
  
  // First, clean up any GDPR scans associated with anonymous user
  await knex('gdpr_scans')
    .where('user_id', ANONYMOUS_USER_UUID)
    .del();
  
  // Then remove the anonymous user
  await knex('users')
    .where('id', ANONYMOUS_USER_UUID)
    .del();
  
  console.log('✅ Anonymous user removed successfully');
}
