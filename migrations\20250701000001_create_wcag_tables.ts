import { Knex } from 'knex';

/**
 * WCAG Compliance Tables Migration
 * Creates all necessary tables for WCAG compliance scanning
 * Following established HIPAA/GDPR patterns with proper foreign keys
 */

export async function up(knex: Knex): Promise<void> {
  // Main WCAG scans table - following GDPR pattern
  await knex.schema.createTable('wcag_scans', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE'); // Proper FK like GDPR
    table.string('target_url', 2048).notNullable();
    table.timestamp('scan_timestamp', { useTz: true }).defaultTo(knex.fn.now());
    table.integer('scan_duration'); // milliseconds - following GDPR pattern
    table.decimal('overall_score', 5, 2); // Following GDPR decimal pattern
    table.enu('scan_status', ['pending', 'running', 'completed', 'failed']).defaultTo('pending');
    table.timestamp('completion_timestamp', { useTz: true });
    table.enu('level_achieved', ['A', 'AA', 'AAA', 'FAIL']);
    table.enu('risk_level', ['critical', 'high', 'medium', 'low']); // Following GDPR enum pattern
    table.integer('perceivable_score');
    table.integer('operable_score');
    table.integer('understandable_score');
    table.integer('robust_score');
    table.integer('wcag21_score');
    table.integer('wcag22_score');
    table.integer('wcag30_score');
    table.integer('total_automated_checks').notNullable(); // Only automated checks
    table.integer('passed_automated_checks').notNullable(); // Only automated passes
    table.integer('failed_automated_checks').notNullable(); // Only automated failures
    table.integer('manual_review_items').defaultTo(0); // Separate manual tracking
    table.jsonb('scan_options');
    table.text('error_message');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes - following HIPAA/GDPR patterns
    table.index('user_id');
    table.index('scan_timestamp');
    table.index('scan_status');
    table.index('level_achieved');
    table.index('risk_level');
  });

  // AUTOMATED check results table - ONLY for automated checks
  await knex.schema.createTable('wcag_automated_results', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('rule_id', 20).notNullable(); // e.g., 'WCAG-001'
    table.string('rule_name', 255).notNullable();
    table.enu('category', ['perceivable', 'operable', 'understandable', 'robust']); // Enum for consistency
    table.enu('wcag_version', ['2.1', '2.2', '3.0']); // Enum for consistency
    table.string('success_criterion', 20); // e.g., '1.1.1', '2.4.11'
    table.enu('level', ['A', 'AA', 'AAA']); // Enum for consistency
    table.enu('status', ['passed', 'failed', 'not_applicable']); // NO manual_review status
    table.integer('score').notNullable(); // Contributes to main score
    table.integer('max_score').notNullable();
    table.decimal('weight', 3, 2).notNullable();
    table.jsonb('evidence'); // array of evidence objects
    table.jsonb('recommendations'); // array of recommendation strings
    table.integer('execution_time'); // milliseconds
    table.text('error_message');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('rule_id');
    table.index('status');
    table.index('category');
    table.index('wcag_version');
    table.index('level');
  });

  // Color contrast analysis table
  await knex.schema.createTable('wcag_contrast_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('element_selector', 1000).notNullable();
    table.string('element_type', 100); // text, button, link, etc.
    table.string('foreground_color', 20).notNullable();
    table.string('background_color', 20).notNullable();
    table.decimal('contrast_ratio', 4, 2).notNullable();
    table.boolean('is_large_text').notNullable();
    table.boolean('level_aa_pass').notNullable();
    table.boolean('level_aaa_pass').notNullable();
    table.text('context_description');
    table.text('recommendation');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('level_aa_pass');
    table.index('level_aaa_pass');
  });

  // Focus analysis table
  await knex.schema.createTable('wcag_focus_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('element_selector', 1000).notNullable();
    table.string('element_type', 100);
    table.boolean('is_focusable').notNullable();
    table.integer('tab_index');
    table.boolean('has_visible_focus').notNullable();
    table.decimal('focus_indicator_width', 4, 2);
    table.string('focus_indicator_color', 20);
    table.decimal('focus_contrast_ratio', 4, 2);
    table.boolean('is_obscured').notNullable();
    table.boolean('keyboard_accessible').notNullable();
    table.jsonb('issues'); // array of focus-related issues
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('is_focusable');
    table.index('has_visible_focus');
    table.index('is_obscured');
    table.index('keyboard_accessible');
  });

  // Keyboard analysis table
  await knex.schema.createTable('wcag_keyboard_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('element_selector', 1000).notNullable();
    table.string('element_type', 100);
    table.boolean('is_reachable').notNullable();
    table.boolean('is_operable').notNullable();
    table.jsonb('supported_keys'); // array of supported key combinations
    table.boolean('has_keyboard_trap').notNullable();
    table.integer('focus_order_position');
    table.boolean('is_logical_order').notNullable();
    table.jsonb('issues'); // array of keyboard-related issues
    table.jsonb('recommendations'); // array of recommendations
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('is_reachable');
    table.index('has_keyboard_trap');
    table.index('is_logical_order');
  });

  // Manual review items table - following GDPR pattern for manual reviews
  await knex.schema.createTable('wcag_manual_reviews', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('wcag_scans').onDelete('CASCADE');
    table.string('rule_id', 20).notNullable();
    table.string('element_selector', 1000);
    table.text('description').notNullable();
    table.text('automated_findings');
    table.text('review_required').notNullable();
    table.enu('priority', ['high', 'medium', 'low']).notNullable();
    table.integer('estimated_time'); // minutes
    table.enu('review_status', ['pending', 'in_progress', 'completed', 'skipped']).defaultTo('pending');
    table.text('reviewer_notes');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('reviewed_at', { useTz: true });

    // Indexes
    table.index('scan_id');
    table.index('rule_id');
    table.index('priority');
    table.index('review_status');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('wcag_manual_reviews');
  await knex.schema.dropTableIfExists('wcag_keyboard_analysis');
  await knex.schema.dropTableIfExists('wcag_focus_analysis');
  await knex.schema.dropTableIfExists('wcag_contrast_analysis');
  await knex.schema.dropTableIfExists('wcag_automated_results');
  await knex.schema.dropTableIfExists('wcag_scans');
}
