/**
 * Component Tests for HIPAA Dashboard
 * Tests rendering, interactions, and accessibility features
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react'; // eslint-disable-line @typescript-eslint/no-unused-vars
import { HipaaDashboard } from '@/components/dashboard/hipaa/HipaaDashboard';
import { HipaaDashboardData } from '@/services/hipaa-dashboard-api';
import { ToastProvider } from '@/components/ui/Toast';
import { announceToScreenReader } from '@/utils/accessibility';

// Mock the responsive hooks
jest.mock('@/hooks/useResponsive', () => ({
  useResponsiveDashboard: () => ({
    layout: { isMobile: false, isTablet: false, isDesktop: true, stackVertically: false },
    spacing: { section: '1.5rem', container: '2rem' },
    cards: { columns: 3, gap: '2rem', padding: '2rem' },
  }),
  useBreakpoint: () => ({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    currentBreakpoint: 'lg',
  }),
}));

// Mock the accessibility utils
jest.mock('@/utils/accessibility', () => ({
  createScoreAriaLabel: (score: number) => `Score: ${score} percent`,
  createRiskAriaLabel: (risk: string) => `Risk level: ${risk}`,
  createLandmarkProps: (role: string, label: string) => ({ role, 'aria-label': label }),
  createHeadingProps: (level: number) => ({ role: 'heading', 'aria-level': level }),
  announceToScreenReader: jest.fn(),
}));

// Mock components that might not be available in test environment
jest.mock('@/components/dashboard/hipaa/HipaaOverviewCard', () => {
  return {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    HipaaOverviewCard: (
      { overallScore, riskLevel, loading }: any, // eslint-disable-line @typescript-eslint/no-explicit-any
    ) => (
      <div data-testid="overview-card">
        {loading ? 'Loading...' : `Score: ${overallScore}, Risk: ${riskLevel}`}
      </div>
    ),
  };
});

jest.mock('@/components/dashboard/hipaa/HipaaModuleCard', () => {
  return {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    HipaaModuleCard: (
      { moduleType, latestScore, loading, onStartScan, onViewResults }: any, // eslint-disable-line @typescript-eslint/no-explicit-any
    ) => (
      <div data-testid={`${moduleType}-module-card`}>
        {loading ? (
          'Loading...'
        ) : (
          <>
            <span>
              Module: {moduleType}, Score: {latestScore}
            </span>
            <button onClick={onStartScan} data-testid={`start-${moduleType}-scan`}>
              Start Scan
            </button>
            <button onClick={onViewResults} data-testid={`view-${moduleType}-results`}>
              View Results
            </button>
          </>
        )}
      </div>
    ),
  };
});

const mockDashboardData: HipaaDashboardData = {
  overview: {
    overallScore: 85,
    riskLevel: 'medium',
    complianceStatus: 'partially_compliant',
    lastScanDate: '2025-06-24T10:30:00Z',
    totalScans: 24,
  },
  privacyModule: {
    latestScore: 82,
    scanCount: 12,
    lastScanDate: '2025-06-24T10:30:00Z',
    status: 'active',
    recentScans: [],
  },
  securityModule: {
    latestScore: 88,
    scanCount: 12,
    lastScanDate: '2025-06-23T14:15:00Z',
    status: 'active',
    recentScans: [],
  },
  recentActivity: [
    {
      id: 'activity-1',
      type: 'privacy',
      url: 'https://example.com',
      timestamp: '2025-06-24T10:30:00Z',
      score: 82,
      status: 'completed',
      riskLevel: 'medium',
    },
    {
      id: 'activity-2',
      type: 'security',
      url: 'https://example.com',
      timestamp: '2025-06-24T11:00:00Z',
      score: 88,
      status: 'completed',
      riskLevel: 'low',
    },
  ],
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(<ToastProvider>{component}</ToastProvider>);
};

describe('HipaaDashboard', () => {
  const mockProps = {
    data: mockDashboardData,
    loading: false,
    error: null,
    onRefresh: jest.fn(),
    onStartPrivacyScan: jest.fn(),
    onStartSecurityScan: jest.fn(),
    onViewPrivacyResults: jest.fn(),
    onViewSecurityResults: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render dashboard header correctly', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
        'HIPAA Compliance Dashboard',
      );
      expect(
        screen.getByText('Comprehensive view of your HIPAA privacy and security compliance'),
      ).toBeInTheDocument();
    });

    it('should render overview and module cards', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      expect(screen.getByTestId('overview-card')).toBeInTheDocument();
      expect(screen.getByTestId('privacy-module-card')).toBeInTheDocument();
      expect(screen.getByTestId('security-module-card')).toBeInTheDocument();
    });

    it('should render recent activity section', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      expect(screen.getByText('Recent Scan Activity')).toBeInTheDocument();
      expect(screen.getByText('https://example.com')).toBeInTheDocument();
    });

    it('should render refresh button when onRefresh is provided', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      expect(screen.getByRole('button', { name: /refresh dashboard data/i })).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    it('should show loading skeletons when loading is true', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} loading={true} />);

      // Should show skeleton loading states
      expect(screen.getAllByText('Loading...')).toHaveLength(3); // Overview + 2 module cards
    });

    it('should disable refresh button when loading', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} loading={true} />);

      const refreshButton = screen.getByRole('button', { name: /refreshing dashboard data/i });
      expect(refreshButton).toBeDisabled();
    });

    it('should show refreshing text when loading', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} loading={true} />);

      expect(screen.getByText('Refreshing...')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should display error message when error occurs', () => {
      const errorMessage = 'Failed to load dashboard data';
      renderWithProviders(<HipaaDashboard {...mockProps} error={errorMessage} />);

      expect(screen.getByText('Error Loading Dashboard')).toBeInTheDocument();
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    it('should show retry button on error', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} error="Network error" />);

      const retryButton = screen.getByRole('button', { name: /try again/i });
      expect(retryButton).toBeInTheDocument();
    });

    it('should call onRefresh when retry button is clicked', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} error="Network error" />);

      const retryButton = screen.getByRole('button', { name: /try again/i });
      fireEvent.click(retryButton);

      expect(mockProps.onRefresh).toHaveBeenCalledTimes(1);
    });
  });

  describe('Interactions', () => {
    it('should call onRefresh when refresh button is clicked', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      const refreshButton = screen.getByRole('button', { name: /refresh dashboard data/i });
      fireEvent.click(refreshButton);

      expect(mockProps.onRefresh).toHaveBeenCalledTimes(1);
    });

    it('should call privacy scan handlers when privacy module buttons are clicked', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      const startScanButton = screen.getByTestId('start-privacy-scan');
      const viewResultsButton = screen.getByTestId('view-privacy-results');

      fireEvent.click(startScanButton);
      fireEvent.click(viewResultsButton);

      expect(mockProps.onStartPrivacyScan).toHaveBeenCalledTimes(1);
      expect(mockProps.onViewPrivacyResults).toHaveBeenCalledTimes(1);
    });

    it('should call security scan handlers when security module buttons are clicked', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      const startScanButton = screen.getByTestId('start-security-scan');
      const viewResultsButton = screen.getByTestId('view-security-results');

      fireEvent.click(startScanButton);
      fireEvent.click(viewResultsButton);

      expect(mockProps.onStartSecurityScan).toHaveBeenCalledTimes(1);
      expect(mockProps.onViewSecurityResults).toHaveBeenCalledTimes(1);
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA landmarks', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      expect(screen.getByRole('main')).toBeInTheDocument();
      expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'HIPAA Compliance Dashboard');
    });

    it('should have proper heading hierarchy', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      const mainHeading = screen.getByRole('heading', { level: 1 });
      expect(mainHeading).toHaveTextContent('HIPAA Compliance Dashboard');
    });

    it('should have proper button labels', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      expect(screen.getByRole('button', { name: /refresh dashboard data/i })).toBeInTheDocument();
      expect(
        screen.getByRole('link', { name: /view detailed compliance reports/i }),
      ).toBeInTheDocument();
    });

    it('should announce loading states to screen readers', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} loading={true} />);

      expect(announceToScreenReader).toHaveBeenCalledWith('Loading HIPAA dashboard data', 'polite');
    });

    it('should announce errors to screen readers', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} error="Network error" />);

      expect(announceToScreenReader).toHaveBeenCalledWith(
        'Error loading dashboard: Network error',
        'assertive',
      );
    });
  });

  describe('Recent Activity', () => {
    it('should display recent activity items', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      expect(screen.getByText('Privacy Policy Scan')).toBeInTheDocument();
      expect(screen.getByText('Security Scan')).toBeInTheDocument();
      expect(screen.getByText('Score: 82%')).toBeInTheDocument();
      expect(screen.getByText('Score: 88%')).toBeInTheDocument();
    });

    it('should show empty state when no recent activity', () => {
      const dataWithoutActivity = {
        ...mockDashboardData,
        recentActivity: [],
      };

      renderWithProviders(<HipaaDashboard {...mockProps} data={dataWithoutActivity} />);

      expect(screen.getByText('No Recent Activity')).toBeInTheDocument();
      expect(screen.getByText('Start a scan to see activity here.')).toBeInTheDocument();
    });

    it('should have proper ARIA labels for activity items', () => {
      renderWithProviders(<HipaaDashboard {...mockProps} />);

      const activityList = screen.getByRole('list', { name: /recent scan activity/i });
      expect(activityList).toBeInTheDocument();

      const activityItems = screen.getAllByRole('listitem');
      expect(activityItems).toHaveLength(2);
    });
  });

  describe('Responsive Behavior', () => {
    it('should handle mobile layout', () => {
      // Mock mobile responsive hook
      jest.doMock('@/hooks/useResponsive', () => ({
        useResponsiveDashboard: () => ({
          layout: { isMobile: true, isTablet: false, isDesktop: false, stackVertically: true },
          spacing: { section: '1rem', container: '1rem' },
          cards: { columns: 1, gap: '1rem', padding: '1rem' },
        }),
        useBreakpoint: () => ({
          isMobile: true,
          isTablet: false,
          isDesktop: false,
          currentBreakpoint: 'sm',
        }),
      }));

      renderWithProviders(<HipaaDashboard {...mockProps} />);

      // Should still render all components but with mobile layout
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      expect(screen.getByTestId('overview-card')).toBeInTheDocument();
    });
  });
});
