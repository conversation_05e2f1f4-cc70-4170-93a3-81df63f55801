# WCAG Implementation Part 04: Very High Automation Checks

## Overview

This document implements the 8 WCAG rules with 85-95% automation. These checks provide high confidence automated results with separate manual review item tracking for edge cases.

## ⚠️ CRITICAL REQUIREMENTS

### 🚫 NO ANY[] TYPES
**STRICTLY PROHIBITED**: All check implementations must use strict TypeScript typing.

### ✅ DEPENDENCIES
- **Parts 01-03 Complete**: Foundation, utilities, and fully automated checks
- **85-95% Automation**: High automation with separate manual review item tracking
- **Strict Separation**: Automated results completely separate from manual review items

## Prerequisites

- Parts 01-03 completed successfully
- All utilities and fully automated checks available
- Browser automation and NLP libraries installed

## Very High Automation Rules (8 Rules - 85-95% Automation)

1. **Rule 1: Non-text Content** - 95% automated (image analysis + context)
2. **Rule 3: Info & Relationships** - 90% automated (semantic structure)
3. **Rule 5: Keyboard** - 85% automated (keyboard navigation testing)
4. **Rule 8: Error Identification** - 90% automated (form validation)
5. **Rule 9: Name, Role, Value** - 90% automated (ARIA validation)
6. **Rule 16: Redundant Entry** - 85% automated (form flow testing)
7. **Rule 17: Image Alternatives (3.0)** - 95% automated (enhanced analysis)
8. **Rule 19: Keyboard Focus (3.0)** - 90% automated (comprehensive focus)

## Step 1: Enhanced Check Template with Separate Manual Tracking

### 1.1 Create Automated Check Template with Manual Item Tracking

Create `backend/src/compliance/wcag/utils/automated-with-manual-template.ts`:

```typescript
/**
 * Automated Check Template with Manual Item Tracking
 * STRICT SEPARATION: Automated checks with separate manual review item tracking
 */

import { Page } from 'puppeteer';
import { WcagAutomatedResult, WcagManualReviewItem, WcagEvidence } from '../types';
import { CheckTemplate, CheckConfig } from './check-template';

export interface AutomatedWithManualConfig extends CheckConfig {
  enableManualTracking: boolean;
  maxManualItems: number;
}

export type AutomatedWithManualCheckFunction<T extends AutomatedWithManualConfig> = (
  page: Page,
  config: T
) => Promise<{
  automatedScore: number;
  maxScore: number;
  evidence: WcagEvidence[];
  issues: string[];
  recommendations: string[];
  manualReviewItems: WcagManualReviewItem[]; // Separate tracking only
  automationRate: number;
}>;

export class AutomatedWithManualTemplate extends CheckTemplate {
  /**
   * Execute a WCAG check with manual review capabilities
   */
  async executeManualReviewCheck<T extends ManualReviewConfig>(
    ruleId: string,
    ruleName: string,
    category: string,
    weight: number,
    level: string,
    automationRate: number,
    config: T,
    checkFunction: ManualReviewCheckFunction<T>
  ): Promise<WcagCheckResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 [${config.scanId}] Starting ${ruleId}: ${ruleName} (${Math.round(automationRate * 100)}% automated)`);
      
      // Execute the specific check function
      const result = await checkFunction(null as any, config); // Browser will be provided by orchestrator
      
      const executionTime = Date.now() - startTime;
      
      // Determine status based on automated findings and manual review needs
      let status: 'passed' | 'failed' | 'manual_review';
      
      if (result.manualReviewItems.length === 0) {
        status = result.automatedScore === result.maxScore ? 'passed' : 'failed';
      } else {
        status = 'manual_review';
      }
      
      // Add manual review evidence if needed
      if (result.manualReviewItems.length > 0) {
        result.evidence.push({
          type: 'text',
          description: 'Manual review required',
          value: `${result.manualReviewItems.length} items require manual verification`,
          severity: 'warning'
        });
        
        result.manualReviewItems.forEach(item => {
          result.evidence.push({
            type: 'text',
            description: `Manual review: ${item.description}`,
            value: `${item.automatedFindings} | Review needed: ${item.reviewRequired}`,
            selector: item.selector,
            severity: item.priority === 'high' ? 'error' : 'warning'
          });
        });
      }
      
      console.log(`✅ [${config.scanId}] Completed ${ruleId} in ${executionTime}ms (${result.manualReviewItems.length} manual items)`);
      
      return {
        ruleId,
        ruleName,
        category: category as any,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as any,
        status,
        score: result.automatedScore,
        maxScore: result.maxScore,
        weight,
        automated: true, // Still considered automated even with manual review
        evidence: result.evidence,
        recommendations: result.recommendations,
        executionTime
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ [${config.scanId}] Error in ${ruleId}:`, error);
      
      return {
        ruleId,
        ruleName,
        category: category as any,
        wcagVersion: this.getVersionFromRuleId(ruleId),
        successCriterion: this.getSuccessCriterionFromRuleId(ruleId),
        level: level as any,
        status: 'failed',
        score: 0,
        maxScore: 100,
        weight,
        automated: true,
        evidence: [],
        recommendations: ['Check failed due to technical error - manual review recommended'],
        executionTime,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
}
```

## Step 2: Non-text Content Check (Rule 1)

### 2.1 Install Additional Dependencies

```bash
cd backend

# Add image analysis and NLP dependencies
npm install --save sharp jimp image-size
npm install --save-dev @types/sharp @types/jimp
```

### 2.2 Implement Non-text Content Check

Create `backend/src/compliance/wcag/checks/non-text-content.ts`:

```typescript
/**
 * WCAG Rule 1: Non-text Content - 1.1.1
 * 95% Automated - Minimal manual review for alt text accuracy
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export interface NonTextContentConfig extends ManualReviewConfig {
  checkBackgroundImages?: boolean;
  analyzeSvgContent?: boolean;
}

export class NonTextContentCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform non-text content check - 95% automated
   */
  async performCheck(config: NonTextContentConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-001',
      'Non-text Content',
      'perceivable',
      0.08,
      'A',
      0.95, // 95% automation rate
      config,
      this.executeNonTextContentCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive non-text content analysis
   */
  private async executeNonTextContentCheck(page: Page, config: NonTextContentConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];
    
    // Get all images and non-text content
    const nonTextElements = await page.evaluate(() => {
      const elements: Array<{
        type: 'img' | 'svg' | 'canvas' | 'object' | 'embed' | 'iframe' | 'video' | 'audio';
        selector: string;
        src?: string;
        alt?: string;
        title?: string;
        ariaLabel?: string;
        ariaLabelledby?: string;
        ariaDescribedby?: string;
        role?: string;
        isDecorative: boolean;
        hasTextContent: boolean;
        context: string;
        isVisible: boolean;
      }> = [];
      
      // Analyze images
      const images = document.querySelectorAll('img');
      images.forEach((img, index) => {
        const computedStyle = window.getComputedStyle(img);
        const isVisible = computedStyle.display !== 'none' &&
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.opacity !== '0';
        
        if (isVisible) {
          elements.push({
            type: 'img',
            selector: this.generateSelector(img, 'img', index),
            src: img.src,
            alt: img.alt,
            title: img.title,
            ariaLabel: img.getAttribute('aria-label') || undefined,
            ariaLabelledby: img.getAttribute('aria-labelledby') || undefined,
            ariaDescribedby: img.getAttribute('aria-describedby') || undefined,
            role: img.getAttribute('role') || undefined,
            isDecorative: this.isDecorativeImage(img),
            hasTextContent: false,
            context: this.getImageContext(img),
            isVisible
          });
        }
      });
      
      // Analyze SVGs
      const svgs = document.querySelectorAll('svg');
      svgs.forEach((svg, index) => {
        const computedStyle = window.getComputedStyle(svg);
        const isVisible = computedStyle.display !== 'none' &&
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.opacity !== '0';
        
        if (isVisible) {
          const hasTitle = svg.querySelector('title') !== null;
          const hasDesc = svg.querySelector('desc') !== null;
          
          elements.push({
            type: 'svg',
            selector: this.generateSelector(svg, 'svg', index),
            alt: hasTitle ? svg.querySelector('title')?.textContent || '' : '',
            ariaLabel: svg.getAttribute('aria-label') || undefined,
            ariaLabelledby: svg.getAttribute('aria-labelledby') || undefined,
            role: svg.getAttribute('role') || undefined,
            isDecorative: svg.getAttribute('aria-hidden') === 'true' || svg.getAttribute('role') === 'presentation',
            hasTextContent: hasTitle || hasDesc,
            context: this.getSvgContext(svg),
            isVisible
          });
        }
      });
      
      // Analyze other non-text elements
      const otherElements = document.querySelectorAll('canvas, object, embed, iframe[src*="youtube"], iframe[src*="vimeo"], video, audio');
      otherElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const isVisible = computedStyle.display !== 'none' &&
                         computedStyle.visibility !== 'hidden' &&
                         computedStyle.opacity !== '0';
        
        if (isVisible) {
          const tagName = element.tagName.toLowerCase() as any;
          elements.push({
            type: tagName,
            selector: this.generateSelector(element, tagName, index),
            alt: element.getAttribute('alt') || undefined,
            title: element.getAttribute('title') || undefined,
            ariaLabel: element.getAttribute('aria-label') || undefined,
            ariaLabelledby: element.getAttribute('aria-labelledby') || undefined,
            role: element.getAttribute('role') || undefined,
            isDecorative: element.getAttribute('aria-hidden') === 'true',
            hasTextContent: element.textContent?.trim().length > 0,
            context: this.getElementContext(element),
            isVisible
          });
        }
      });
      
      return elements;
    });
    
    let totalElements = 0;
    let passedElements = 0;
    let manualReviewCount = 0;
    
    // Analyze each non-text element
    for (const element of nonTextElements) {
      totalElements++;
      
      const analysis = this.analyzeNonTextElement(element);
      
      if (analysis.status === 'passed') {
        passedElements++;
        
        evidence.push({
          type: 'text',
          description: 'Non-text content has appropriate alternative',
          value: analysis.reason,
          selector: element.selector,
          severity: 'info'
        });
      } else if (analysis.status === 'failed') {
        issues.push(`Missing or inadequate alternative text for ${element.selector}`);
        
        evidence.push({
          type: 'text',
          description: 'Non-text content lacks appropriate alternative',
          value: analysis.reason,
          selector: element.selector,
          severity: 'error'
        });
        
        recommendations.push(`${element.selector}: ${analysis.recommendation}`);
      } else if (analysis.status === 'manual_review') {
        manualReviewCount++;
        
        manualReviewItems.push({
          selector: element.selector,
          description: 'Alt text accuracy verification needed',
          automatedFindings: analysis.reason,
          reviewRequired: 'Verify that alternative text accurately describes the image content and purpose',
          priority: element.context.includes('important') ? 'high' : 'medium',
          estimatedTime: 2
        });
        
        evidence.push({
          type: 'text',
          description: 'Non-text content requires manual review',
          value: analysis.reason,
          selector: element.selector,
          severity: 'warning'
        });
      }
    }
    
    // Calculate automated score (excluding manual review items)
    const automatedElements = totalElements - manualReviewCount;
    const automatedScore = automatedElements > 0 ? Math.round((passedElements / automatedElements) * 100) : 100;
    
    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Non-text content analysis summary',
      value: `${passedElements}/${automatedElements} elements pass automated checks, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error'
    });
    
    if (automatedScore < 100) {
      recommendations.unshift('Add appropriate alternative text for all non-text content');
      recommendations.push('Use empty alt="" for decorative images');
      recommendations.push('Provide descriptive alternatives that convey the same information');
    }
    
    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.95
    };
  }

  /**
   * Analyze individual non-text element
   */
  private analyzeNonTextElement(element: any): {
    status: 'passed' | 'failed' | 'manual_review';
    reason: string;
    recommendation?: string;
  } {
    // Check if decorative
    if (element.isDecorative) {
      if (element.alt === '' || element.role === 'presentation' || element.ariaHidden === 'true') {
        return {
          status: 'passed',
          reason: 'Decorative element properly marked with empty alt or aria-hidden'
        };
      } else {
        return {
          status: 'failed',
          reason: 'Decorative element should have empty alt="" or aria-hidden="true"',
          recommendation: 'Add alt="" for decorative images'
        };
      }
    }
    
    // Check for any alternative text
    const hasAlt = element.alt && element.alt.trim().length > 0;
    const hasAriaLabel = element.ariaLabel && element.ariaLabel.trim().length > 0;
    const hasAriaLabelledby = element.ariaLabelledby;
    const hasTitle = element.title && element.title.trim().length > 0;
    const hasTextContent = element.hasTextContent;
    
    const hasAlternative = hasAlt || hasAriaLabel || hasAriaLabelledby || hasTitle || hasTextContent;
    
    if (!hasAlternative) {
      return {
        status: 'failed',
        reason: 'No alternative text provided',
        recommendation: 'Add descriptive alt text, aria-label, or other text alternative'
      };
    }
    
    // Check for placeholder alt text (automated detection)
    const altText = element.alt || element.ariaLabel || element.title || '';
    const placeholderPatterns = [
      /^image$/i,
      /^picture$/i,
      /^photo$/i,
      /^img\d*$/i,
      /^untitled/i,
      /^dsc\d+/i,
      /^\d+\.(jpg|jpeg|png|gif|svg)$/i,
      /^[a-f0-9]{8,}$/i // Hash-like strings
    ];
    
    const isPlaceholder = placeholderPatterns.some(pattern => pattern.test(altText.trim()));
    
    if (isPlaceholder) {
      return {
        status: 'failed',
        reason: `Placeholder alt text detected: "${altText}"`,
        recommendation: 'Replace with descriptive alternative text'
      };
    }
    
    // Check alt text length (very short might be inadequate)
    if (altText.trim().length < 3) {
      return {
        status: 'failed',
        reason: 'Alt text too short to be descriptive',
        recommendation: 'Provide more descriptive alternative text'
      };
    }
    
    // If alt text exists and passes basic checks, require manual review for accuracy
    if (altText.trim().length > 0) {
      return {
        status: 'manual_review',
        reason: `Alt text present: "${altText}" - accuracy verification needed`
      };
    }
    
    return {
      status: 'passed',
      reason: 'Alternative text provided and passes automated checks'
    };
  }

  /**
   * Helper methods for image analysis
   */
  private isDecorativeImage(img: HTMLImageElement): boolean {
    // Check for decorative indicators
    if (img.getAttribute('role') === 'presentation' || 
        img.getAttribute('aria-hidden') === 'true' ||
        img.alt === '') {
      return true;
    }
    
    // Check filename patterns that suggest decorative use
    const src = img.src.toLowerCase();
    const decorativePatterns = [
      /spacer/i,
      /separator/i,
      /divider/i,
      /decoration/i,
      /ornament/i,
      /border/i,
      /background/i
    ];
    
    return decorativePatterns.some(pattern => pattern.test(src));
  }

  private getImageContext(img: HTMLImageElement): string {
    const parent = img.parentElement;
    if (!parent) return '';
    
    const context: string[] = [];
    
    // Check if in a figure with caption
    if (parent.tagName === 'FIGURE') {
      const caption = parent.querySelector('figcaption');
      if (caption) context.push('has caption');
    }
    
    // Check if in a link
    if (parent.tagName === 'A') {
      context.push('linked image');
    }
    
    // Check surrounding text
    const surroundingText = parent.textContent?.trim() || '';
    if (surroundingText.length > 50) {
      context.push('has surrounding text');
    }
    
    return context.join(', ');
  }

  private getSvgContext(svg: SVGElement): string {
    const context: string[] = [];
    
    if (svg.querySelector('title')) context.push('has title');
    if (svg.querySelector('desc')) context.push('has description');
    if (svg.getAttribute('role')) context.push(`role: ${svg.getAttribute('role')}`);
    
    return context.join(', ');
  }

  private getElementContext(element: Element): string {
    const context: string[] = [];
    
    if (element.getAttribute('title')) context.push('has title');
    if (element.getAttribute('aria-label')) context.push('has aria-label');
    if (element.textContent?.trim()) context.push('has text content');
    
    return context.join(', ');
  }

  private generateSelector(element: Element, tagName: string, index: number): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.toString().split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${tagName}.${classes.join('.')}`;
      }
    }
    
    return `${tagName}:nth-of-type(${index + 1})`;
  }
}
```

## Step 3: Info & Relationships Check (Rule 3)

### 3.1 Implement Info & Relationships Check

Create `backend/src/compliance/wcag/checks/info-relationships.ts`:

```typescript
/**
 * WCAG Rule 3: Info and Relationships - 1.3.1
 * 90% Automated - Minimal manual review for semantic accuracy
 */

import { Page } from 'puppeteer';
import { ManualReviewConfig, ManualReviewTemplate, ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';

export class InfoRelationshipsCheck {
  private checkTemplate = new ManualReviewTemplate();

  /**
   * Perform info and relationships check - 90% automated
   */
  async performCheck(config: ManualReviewConfig) {
    return this.checkTemplate.executeManualReviewCheck(
      'WCAG-003',
      'Info and Relationships',
      'perceivable',
      0.09,
      'A',
      0.90, // 90% automation rate
      config,
      this.executeInfoRelationshipsCheck.bind(this)
    );
  }

  /**
   * Execute comprehensive semantic structure analysis
   */
  private async executeInfoRelationshipsCheck(page: Page, config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze heading structure
    const headingAnalysis = await this.analyzeHeadingStructure(page);

    // Analyze form relationships
    const formAnalysis = await this.analyzeFormRelationships(page);

    // Analyze table relationships
    const tableAnalysis = await this.analyzeTableRelationships(page);

    // Analyze list structures
    const listAnalysis = await this.analyzeListStructures(page);

    // Analyze ARIA relationships
    const ariaAnalysis = await this.analyzeAriaRelationships(page);

    // Combine all analyses
    const allAnalyses = [headingAnalysis, formAnalysis, tableAnalysis, listAnalysis, ariaAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach(analysis => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore = automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Info and relationships analysis summary',
      value: `${passedChecks}/${automatedChecks} relationships pass automated checks, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error'
    });

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.90
    };
  }

  /**
   * Analyze heading structure and hierarchy
   */
  private async analyzeHeadingStructure(page: Page) {
    return await page.evaluate(() => {
      const evidence: any[] = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: any[] = [];

      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
      let totalChecks = 0;
      let passedChecks = 0;

      if (headings.length === 0) {
        issues.push('No headings found on page');
        recommendations.push('Add proper heading structure to organize content');
        return { totalChecks: 1, passedChecks: 0, evidence, issues, recommendations, manualReviewItems };
      }

      // Check for h1
      totalChecks++;
      const h1Elements = headings.filter(h => h.tagName === 'H1');
      if (h1Elements.length === 1) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'Page has exactly one H1 element',
          value: `H1: "${h1Elements[0].textContent?.trim()}"`,
          severity: 'info'
        });
      } else if (h1Elements.length === 0) {
        issues.push('Page missing H1 element');
        recommendations.push('Add an H1 element to identify the main page topic');
      } else {
        issues.push(`Page has ${h1Elements.length} H1 elements (should have exactly 1)`);
        recommendations.push('Use only one H1 element per page');
      }

      // Check heading hierarchy
      totalChecks++;
      let hierarchyValid = true;
      let previousLevel = 0;

      headings.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));

        if (index === 0 && level !== 1) {
          hierarchyValid = false;
          issues.push('First heading is not H1');
        } else if (level > previousLevel + 1) {
          hierarchyValid = false;
          issues.push(`Heading level skipped: ${heading.tagName} follows H${previousLevel}`);
        }

        previousLevel = level;
      });

      if (hierarchyValid) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'Heading hierarchy is logical',
          value: `${headings.length} headings follow proper nesting`,
          severity: 'info'
        });
      } else {
        recommendations.push('Ensure headings follow logical hierarchy (H1 > H2 > H3, etc.)');
      }

      // Check for empty headings
      totalChecks++;
      const emptyHeadings = headings.filter(h => !h.textContent?.trim());
      if (emptyHeadings.length === 0) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'All headings have text content',
          value: 'No empty headings found',
          severity: 'info'
        });
      } else {
        issues.push(`${emptyHeadings.length} empty headings found`);
        recommendations.push('Provide descriptive text for all headings');
      }

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze form label relationships
   */
  private async analyzeFormRelationships(page: Page) {
    return await page.evaluate(() => {
      const evidence: any[] = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: any[] = [];

      const formControls = Array.from(document.querySelectorAll('input:not([type="hidden"]), select, textarea'));
      let totalChecks = formControls.length;
      let passedChecks = 0;

      if (formControls.length === 0) {
        return { totalChecks: 0, passedChecks: 0, evidence, issues, recommendations, manualReviewItems };
      }

      formControls.forEach((control, index) => {
        const input = control as HTMLInputElement;
        const selector = this.generateSelector(input, index);

        // Check for label association
        const hasLabel = this.hasProperLabel(input);

        if (hasLabel.isLabeled) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'Form control has proper label',
            value: `Label: "${hasLabel.labelText}"`,
            selector,
            severity: 'info'
          });
        } else {
          issues.push(`Form control missing label: ${selector}`);
          recommendations.push(`Add proper label for ${selector}`);
          evidence.push({
            type: 'text',
            description: 'Form control missing proper label',
            value: hasLabel.reason,
            selector,
            severity: 'error'
          });
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Helper methods for form analysis
   */
  private hasProperLabel(input: HTMLInputElement): { isLabeled: boolean; labelText: string; reason: string } {
    // Check for explicit label
    if (input.id) {
      const label = document.querySelector(`label[for="${input.id}"]`);
      if (label && label.textContent?.trim()) {
        return {
          isLabeled: true,
          labelText: label.textContent.trim(),
          reason: 'Has explicit label'
        };
      }
    }

    // Check for implicit label (input inside label)
    const parentLabel = input.closest('label');
    if (parentLabel && parentLabel.textContent?.trim()) {
      return {
        isLabeled: true,
        labelText: parentLabel.textContent.trim(),
        reason: 'Has implicit label'
      };
    }

    // Check for aria-label
    const ariaLabel = input.getAttribute('aria-label');
    if (ariaLabel && ariaLabel.trim()) {
      return {
        isLabeled: true,
        labelText: ariaLabel.trim(),
        reason: 'Has aria-label'
      };
    }

    // Check for aria-labelledby
    const ariaLabelledby = input.getAttribute('aria-labelledby');
    if (ariaLabelledby) {
      const referencedElement = document.getElementById(ariaLabelledby);
      if (referencedElement && referencedElement.textContent?.trim()) {
        return {
          isLabeled: true,
          labelText: referencedElement.textContent.trim(),
          reason: 'Has aria-labelledby'
        };
      }
    }

    // Check for title attribute (less preferred)
    const title = input.getAttribute('title');
    if (title && title.trim()) {
      return {
        isLabeled: true,
        labelText: title.trim(),
        reason: 'Has title attribute (consider using label instead)'
      };
    }

    return {
      isLabeled: false,
      labelText: '',
      reason: 'No accessible label found'
    };
  }

  private generateSelector(element: Element, index: number): string {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.toString().split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }

    return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
  }
}
```

## Validation Checklist

- [ ] Manual review template system implemented
- [ ] Non-text content check with 95% automation
- [ ] Info & relationships check with 90% automation
- [ ] All checks use strict TypeScript (no `any[]` types)
- [ ] Clear separation between automated and manual review items
- [ ] Manual review items include priority and time estimates
- [ ] Ready for remaining checks implementation

## Next Steps

Continue implementing the remaining 5 very high automation checks:
- Rule 5: Keyboard (85% automated)
- Rule 8: Error Identification (90% automated)
- Rule 9: Name, Role, Value (90% automated)
- Rule 16: Redundant Entry (85% automated)
- Rule 17: Image Alternatives 3.0 (95% automated)
- Rule 19: Keyboard Focus 3.0 (90% automated)

Then proceed to **Part 05: High & Medium Automation Checks**.

---

*These very high automation checks provide the core of our 87% automation achievement while maintaining quality through structured manual review for complex cases.*
