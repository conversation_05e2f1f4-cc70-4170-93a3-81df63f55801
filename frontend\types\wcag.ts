/**
 * Frontend WCAG Type Definitions
 * Client-side types for WCAG functionality
 */

// Re-export backend types for frontend use
export type {
  WcagScanConfig,
  WcagScanOptions,
  WcagScanResult,
  WcagScanSummary,
  WcagCheckResult,
  WcagEvidence,
  WcagRecommendation,
  ContrastAnalysisResult,
  FocusAnalysisResult,
  KeyboardAnalysisResult,
  WcagVersion,
  WcagLevel,
  WcagCategory,
  ScanStatus,
  AutomatedCheckStatus,
  RiskLevel
} from '../../../backend/src/compliance/wcag/types';

// Frontend-specific types
export interface WcagScanFormData {
  targetUrl: string;
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;
}

export interface WcagDashboardState {
  currentScan?: WcagScanResult;
  recentScans: WcagScanResult[];
  isScanning: boolean;
  scanProgress: number;
  error?: string;
  selectedScanId?: string;
}

export interface WcagComponentProps {
  scanResult?: WcagScanResult;
  onScanStart?: (config: WcagScanConfig) => void;
  onExport?: (scanId: string, format: 'pdf' | 'json' | 'csv') => void;
  onRescan?: (scanId: string) => void;
  onDelete?: (scanId: string) => void;
}

export interface ScanProgressInfo {
  scanId: string;
  status: ScanStatus;
  currentCheck?: string;
  completedChecks: number;
  totalChecks: number;
  progress: number;
  estimatedTimeRemaining?: number;
}

export interface QueueStatusInfo {
  totalQueued: number;
  currentlyProcessing: number;
  maxConcurrent: number;
  averageWaitTime: number;
  estimatedWaitTime: number;
  position?: number;
}

// UI State types
export interface WcagUIState {
  activeTab: 'overview' | 'details' | 'recommendations' | 'history';
  selectedCategory?: WcagCategory;
  selectedLevel?: WcagLevel;
  showOnlyFailed: boolean;
  sortBy: 'ruleId' | 'score' | 'category' | 'level';
  sortOrder: 'asc' | 'desc';
  pageSize: number;
  currentPage: number;
}

// Chart data types
export interface ScoreChartData {
  category: string;
  score: number;
  maxScore: number;
  color: string;
}

export interface TrendChartData {
  date: string;
  score: number;
  level: string;
}

export interface ComplianceBreakdownData {
  level: WcagLevel;
  passed: number;
  failed: number;
  total: number;
  percentage: number;
}
