/**
 * Frontend WCAG Type Definitions
 * Client-side types for WCAG functionality
 */

// Re-export backend types for frontend use
export type {
  WcagScanConfig,
  WcagScanOptions,
  WcagScanResult,
  WcagScanSummary,
  WcagCheckResult,
  WcagEvidence,
  WcagRecommendation,
  ContrastAnalysisResult,
  FocusAnalysisResult,
  KeyboardAnalysisResult,
  WcagVersion,
  WcagLevel,
  WcagCategory,
  ScanStatus,
  AutomatedCheckStatus,
  RiskLevel
} from '../../../backend/src/compliance/wcag/types';

// Frontend-specific types
export interface WcagScanFormData {
  targetUrl: string;
  enableContrastAnalysis: boolean;
  enableKeyboardTesting: boolean;
  enableFocusAnalysis: boolean;
  enableSemanticValidation: boolean;
  wcagVersion: WcagVersion | 'all';
  level: WcagLevel;
  maxPages: number;
}

export interface WcagDashboardState {
  currentScan?: WcagScanResult;
  recentScans: WcagScanResult[];
  isScanning: boolean;
  scanProgress: number;
  error?: string;
}

export interface WcagComponentProps {
  scanResult?: WcagScanResult;
  onScanStart?: (config: WcagScanConfig) => void;
  onExport?: () => void;
  onRescan?: () => void;
}
