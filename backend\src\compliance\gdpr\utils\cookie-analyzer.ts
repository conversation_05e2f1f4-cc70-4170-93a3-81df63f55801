import { <PERSON><PERSON>, CookieAnalysisResult } from '../types';

export class CookieAnalyzer {
  /**
   * Classify cookies by category - REAL cookie analysis
   */
  static classifyCookies(cookies: <PERSON><PERSON>[]): {
    essential: <PERSON><PERSON>[];
    analytics: <PERSON><PERSON>[];
    marketing: <PERSON><PERSON>[];
    functional: <PERSON><PERSON>[];
    unclassified: <PERSON><PERSON>[];
  } {
    const classification = {
      essential: [] as <PERSON><PERSON>[],
      analytics: [] as <PERSON><PERSON>[],
      marketing: [] as <PERSON><PERSON>[],
      functional: [] as <PERSON><PERSON>[],
      unclassified: [] as <PERSON><PERSON>[],
    };

    for (const cookie of cookies) {
      const category = this.categorizeCookie(cookie);
      classification[category].push(cookie);
    }

    return classification;
  }

  /**
   * Categorize individual cookie based on name and domain
   */
  private static categorizeCookie(
    cookie: <PERSON>ie,
  ): 'essential' | 'analytics' | 'marketing' | 'functional' | 'unclassified' {
    const name = cookie.name.toLowerCase();
    const domain = cookie.domain.toLowerCase();

    // Essential cookies (session, security, functionality)
    const essentialPatterns = [
      /^(session|sess|jsessionid|phpsessid|asp\.net_sessionid)/,
      /^(csrf|xsrf|_token)/,
      /^(auth|login|user)/,
      /^(cart|basket|shopping)/,
      /^(lang|language|locale)/,
      /^(currency|region|country)/,
    ];

    if (essentialPatterns.some((pattern) => pattern.test(name))) {
      return 'essential';
    }

    // Analytics cookies
    const analyticsPatterns = [
      /^(_ga|_gid|_gat|__utm)/,
      /^(_hjid|_hjIncludedInSample)/,
      /^(adobe|omniture|s_cc|s_sq)/,
      /^(mixpanel|mp_)/,
      /^(amplitude|_amplitude)/,
    ];

    const analyticsDomains = [
      'google-analytics.com',
      'googletagmanager.com',
      'hotjar.com',
      'adobe.com',
      'mixpanel.com',
      'amplitude.com',
    ];

    if (
      analyticsPatterns.some((pattern) => pattern.test(name)) ||
      analyticsDomains.some((d) => domain.includes(d))
    ) {
      return 'analytics';
    }

    // Marketing/Advertising cookies
    const marketingPatterns = [
      /^(fb|facebook|_fbp|_fbc)/,
      /^(twitter|twtr)/,
      /^(linkedin|li_)/,
      /^(doubleclick|__gads)/,
      /^(adsystem|adnxs)/,
    ];

    const marketingDomains = [
      'facebook.com',
      'twitter.com',
      'linkedin.com',
      'doubleclick.net',
      'adsystem.amazon.com',
      'adnxs.com',
    ];

    if (
      marketingPatterns.some((pattern) => pattern.test(name)) ||
      marketingDomains.some((d) => domain.includes(d))
    ) {
      return 'marketing';
    }

    // Functional cookies
    const functionalPatterns = [
      /^(preferences|prefs|settings)/,
      /^(theme|style|layout)/,
      /^(notification|alert)/,
      /^(chat|support|help)/,
    ];

    if (functionalPatterns.some((pattern) => pattern.test(name))) {
      return 'functional';
    }

    return 'unclassified';
  }

  /**
   * Analyze cookie attributes for security compliance
   */
  static analyzeCookieAttributes(cookies: Cookie[]): CookieAnalysisResult[] {
    return cookies.map((cookie) => {
      const issues: string[] = [];
      const recommendations: string[] = [];

      // Check Secure flag for HTTPS cookies
      if (!cookie.secure && cookie.domain.startsWith('https')) {
        issues.push('Missing Secure flag for HTTPS cookie');
        recommendations.push('Add Secure flag to prevent transmission over HTTP');
      }

      // Check HttpOnly flag for session cookies
      if (this.isSessionCookie(cookie) && !cookie.httpOnly) {
        issues.push('Missing HttpOnly flag for session cookie');
        recommendations.push('Add HttpOnly flag to prevent XSS attacks');
      }

      // Check SameSite attribute
      if (!cookie.sameSite || cookie.sameSite === 'None') {
        if (cookie.sameSite === 'None' && !cookie.secure) {
          issues.push('SameSite=None requires Secure flag');
          recommendations.push('Add Secure flag when using SameSite=None');
        }
        if (!cookie.sameSite) {
          recommendations.push('Consider adding SameSite attribute for CSRF protection');
        }
      }

      return {
        cookieId: `${cookie.domain}-${cookie.name}`,
        category: this.categorizeCookie(cookie),
        hasConsent: false, // Will be determined by consent analysis
        complianceIssues: issues,
        recommendations,
      };
    });
  }

  /**
   * Check if cookie is likely a session cookie
   */
  private static isSessionCookie(cookie: Cookie): boolean {
    const sessionPatterns = [
      /^(session|sess|jsessionid|phpsessid|asp\.net_sessionid)/i,
      /^(auth|login|user|token)/i,
    ];

    return sessionPatterns.some((pattern) => pattern.test(cookie.name)) || !cookie.expires; // Session cookies typically don't have expiry
  }

  /**
   * Monitor cookie changes before and after consent
   */
  static compareCookieStates(
    beforeConsent: Cookie[],
    afterConsent: Cookie[],
  ): {
    newCookies: Cookie[];
    blockedCookies: Cookie[];
    persistentCookies: Cookie[];
  } {
    const beforeMap = new Map(beforeConsent.map((c) => [`${c.domain}-${c.name}`, c]));
    const afterMap = new Map(afterConsent.map((c) => [`${c.domain}-${c.name}`, c]));

    const newCookies: Cookie[] = [];
    const blockedCookies: Cookie[] = [];
    const persistentCookies: Cookie[] = [];

    // Find new cookies (appeared after consent)
    for (const [key, cookie] of afterMap) {
      if (!beforeMap.has(key)) {
        newCookies.push(cookie);
      } else {
        persistentCookies.push(cookie);
      }
    }

    // Find blocked cookies (disappeared after consent rejection)
    for (const [key, cookie] of beforeMap) {
      if (!afterMap.has(key)) {
        blockedCookies.push(cookie);
      }
    }

    return { newCookies, blockedCookies, persistentCookies };
  }
}
