import { QuickCheckTemplate } from '../utils/quick-check-template';
import { GdprCheckResult } from '../types';

export interface ImprintContactCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class ImprintContactCheck {
  /**
   * Check for imprint and contact details
   * REAL ANALYSIS - scans for imprint/contact content
   */
  async performCheck(config: ImprintContactCheckConfig): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-021',
      'Imprint & Contact Details',
      'organizational',
      2,
      'low',
      [
        'imprint',
        'impressum',
        'contact',
        'address',
        'company information',
        'legal notice',
        'contact us',
        'about us',
        'company details',
        'business address',
        'registered office',
      ],
      config,
      false, // Can be automated
      60, // Higher threshold as contact info should be easily found
    );
  }
}
