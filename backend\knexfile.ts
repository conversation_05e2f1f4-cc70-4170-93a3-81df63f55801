import * as path from 'path';
import { env } from '../lib/env'; // Using the validated environment variables

interface KnexConfig {
  [key: string]: object;
}

const config: KnexConfig = {
  development: {
    client: 'pg',
    connection: env.DATABASE_URL,
    migrations: {
      directory: path.join(__dirname, '../migrations'), // Pointing to root migrations
      tableName: 'knex_migrations',
      extension: 'ts', // If you plan to write migrations in TypeScript
    },
    seeds: {
      directory: path.join(__dirname, '../seeds'), // Pointing to root seeds directory
      extension: 'ts', // If you plan to write seeds in TypeScript
    },
  },

  staging: {
    client: 'pg',
    connection: env.DATABASE_URL, // Will use staging DATABASE_URL
    migrations: {
      directory: path.join(__dirname, '../migrations'),
      tableName: 'knex_migrations',
      extension: 'ts',
    },
    seeds: {
      directory: path.join(__dirname, '../seeds/staging'),
      extension: 'ts',
    },
  },

  test: {
    client: 'pg',
    connection: env.DATABASE_URL, // This will use the DATABASE_URL from the 'test' environment
    migrations: {
      directory: path.join(__dirname, '../migrations'),
      tableName: 'knex_migrations',
      extension: 'ts',
    },
    seeds: {
      directory: path.join(__dirname, '../seeds/test'), // Optional: if you have test-specific seeds
      extension: 'ts',
    },
  },

  production: {
    client: 'pg',
    connection: env.DATABASE_URL, // Will use production DATABASE_URL
    migrations: {
      directory: path.join(__dirname, '../migrations'),
      tableName: 'knex_migrations',
      extension: 'ts',
    },
    // Seeds are typically not run in production automatically
  },
};

export default config; // CommonJS export for Knex CLI compatibility if not using ESM for CLI

// If Knex CLI has issues with ES module exports from a .ts file,
// you might need to compile this to a .js file (e.g., in a build step)
// or use a .js knexfile that requires the compiled .ts output.
// For now, we assume ts-node can handle it if CLI is invoked correctly.
