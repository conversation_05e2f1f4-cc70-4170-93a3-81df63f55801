import { Router, Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { GdprOrchestrator } from '../../compliance/gdpr/orchestrator';
import { GdprScanRequestSchema } from '../../compliance/gdpr/types';
import { InputValidator, AuditLogger } from '../../config/security';
import { GdprDatabase } from '../../compliance/gdpr/database/gdpr-database';
import { keycloak } from '../../lib/keycloak';
import db from '../../lib/db';

// Import anonymous user utility
const { ensureAnonymousUser, ANONYMOUS_USER_UUID } = require('../../../ensure-anonymous-user');

// Import express-validator functions directly
const expressValidator = require('express-validator');
const { body, param, query, validationResult } = expressValidator;

// Define interfaces for Keycloak token and authenticated request
interface KeycloakTokenContent {
  sub: string;
  email?: string;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
}

interface AuthenticatedRequest extends Request {
  kauth?: {
    grant?: {
      access_token?: {
        content: KeycloakTokenContent;
      };
    };
  };
}

const router = Router();
const orchestrator = new GdprOrchestrator();

/**
 * Helper function to get authenticated user ID from Keycloak token
 */
async function getAuthenticatedUserId(req: AuthenticatedRequest): Promise<string> {
  const authHeader = req.headers.authorization;
  const tokenContent = req.kauth?.grant?.access_token?.content;

  console.log('🔍 Authentication debug:', {
    hasAuthHeader: !!authHeader,
    authHeaderPrefix: authHeader?.substring(0, 20),
    hasKauth: !!req.kauth,
    hasGrant: !!req.kauth?.grant,
    hasAccessToken: !!req.kauth?.grant?.access_token,
    hasTokenContent: !!tokenContent,
    tokenSub: tokenContent?.sub,
  });

  if (!tokenContent?.sub) {
    console.log('🔍 No authenticated user found, using anonymous user');
    await ensureAnonymousUser();
    return ANONYMOUS_USER_UUID;
  }

  const keycloakId = tokenContent.sub;
  console.log('🔑 Keycloak ID from token:', keycloakId);

  try {
    // Find or create user in our database
    let user = await db('users').where({ keycloak_id: keycloakId }).first();

    if (!user) {
      console.log('📝 Creating new user from Keycloak token');
      const [newUser] = await db('users')
        .insert({
          keycloak_id: keycloakId,
          email: tokenContent.email || `user-${keycloakId}@comply-checker.local`,
          created_at: new Date(),
          updated_at: new Date(),
        })
        .returning('*');
      user = newUser;
      console.log('✅ New user created:', { id: user.id, email: user.email });
    } else {
      console.log('✅ User found:', { id: user.id, email: user.email });
    }

    return user.id;
  } catch (error) {
    console.error('❌ Error getting authenticated user:', error);
    await ensureAnonymousUser();
    return ANONYMOUS_USER_UUID;
  }
}

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// Security validation middleware
const validateSecurity = (req: Request, res: Response, next: NextFunction) => {
  const { targetUrl } = req.body;

  if (!InputValidator.isValidUrl(targetUrl)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid URL',
      message: 'The provided URL is not valid or is blocked for security reasons',
    });
  }

  next();
};

/**
 * @openapi
 * tags:
 *   name: GDPR Compliance
 *   description: Routes for GDPR specific compliance checks and operations.
 */

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan:
 *   post:
 *     summary: Start a comprehensive GDPR compliance scan
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - targetUrl
 *             properties:
 *               targetUrl:
 *                 type: string
 *                 format: uri
 *                 description: The URL to scan for GDPR compliance
 *               scanOptions:
 *                 type: object
 *                 properties:
 *                   enableCookieAnalysis:
 *                     type: boolean
 *                     default: true
 *                   enableTrackerDetection:
 *                     type: boolean
 *                     default: true
 *                   enableConsentTesting:
 *                     type: boolean
 *                     default: true
 *                   maxPages:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 50
 *                     default: 10
 *                   timeout:
 *                     type: integer
 *                     minimum: 60000
 *                     maximum: 3600000
 *                     default: 300000
 *     responses:
 *       200:
 *         description: GDPR scan completed successfully
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/scan',
  keycloak.protect(), // Add Keycloak authentication protection
  [
    // Input validation
    body('targetUrl')
      .isURL({ protocols: ['http', 'https'], require_protocol: true })
      .withMessage('Valid URL is required')
      .isLength({ max: 2048 })
      .withMessage('URL must be less than 2048 characters'),

    body('scanOptions.enableCookieAnalysis')
      .optional()
      .isBoolean()
      .withMessage('enableCookieAnalysis must be a boolean'),

    body('scanOptions.enableTrackerDetection')
      .optional()
      .isBoolean()
      .withMessage('enableTrackerDetection must be a boolean'),

    body('scanOptions.enableConsentTesting')
      .optional()
      .isBoolean()
      .withMessage('enableConsentTesting must be a boolean'),

    body('scanOptions.maxPages')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('maxPages must be between 1 and 50'),

    body('scanOptions.timeout')
      .optional()
      .isInt({ min: 60000, max: 3600000 })
      .withMessage('timeout must be between 60000 and 3600000 milliseconds'),

    // Security validation
    validateRequest,
    validateSecurity,
  ],
  async (req: AuthenticatedRequest, res: Response) => {
    const requestId = uuidv4();
    const startTime = Date.now();

    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.warn(`⚠️ [${requestId}] GDPR scan validation failed:`, errors.array());
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid request parameters',
          details: errors.array(),
          requestId,
        });
      }

      // Parse and validate request with Zod
      const parseResult = GdprScanRequestSchema.safeParse(req.body);
      if (!parseResult.success) {
        console.warn(`⚠️ [${requestId}] GDPR scan Zod validation failed:`, parseResult.error);
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid request schema',
          details: parseResult.error.errors,
          requestId,
        });
      }

      const scanRequest = parseResult.data;

      // Get authenticated user ID (with fallback to anonymous)
      const userId = await getAuthenticatedUserId(req);

      console.log(`🚀 [${requestId}] Starting GDPR compliance scan:`, {
        targetUrl: scanRequest.targetUrl,
        userId,
        scanOptions: scanRequest.scanOptions,
      });

      // Log audit trail
      AuditLogger.logSecurityEvent(
        'GDPR_SCAN_INITIATED',
        {
          userId,
          resource: scanRequest.targetUrl,
          requestId,
          scanOptions: scanRequest.scanOptions,
        },
        req,
      );

      // Execute GDPR compliance scan
      const result = await orchestrator.performComprehensiveScan(userId, scanRequest);

      const processingTime = Date.now() - startTime;

      console.log(`✅ [${requestId}] GDPR scan completed successfully:`, {
        scanId: result.scanId,
        overallScore: result.overallScore,
        riskLevel: result.riskLevel,
        processingTime: `${processingTime}ms`,
      });

      // Log successful completion
      AuditLogger.logSecurityEvent(
        'GDPR_SCAN_COMPLETED',
        {
          userId,
          resource: scanRequest.targetUrl,
          requestId,
          scanId: result.scanId,
          overallScore: result.overallScore,
          riskLevel: result.riskLevel,
          processingTime,
        },
        req,
      );

      res.json({
        success: true,
        data: result,
        requestId,
        processingTime,
      });
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`❌ [${requestId}] GDPR scan failed:`, error);

      // Log error
      AuditLogger.logSecurityEvent(
        'GDPR_SCAN_FAILED',
        {
          userId: await getAuthenticatedUserId(req),
          resource: req.body.targetUrl || 'unknown',
          requestId,
          error: error instanceof Error ? error.message : 'Unknown error',
          processingTime,
        },
        req,
      );

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'GDPR compliance scan failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  },
);

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan/{scanId}:
 *   get:
 *     summary: Get GDPR scan result by ID
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scanId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The scan ID to retrieve
 *     responses:
 *       200:
 *         description: Scan result retrieved successfully
 *       404:
 *         description: Scan not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/scan/:scanId',
  [param('scanId').isUUID().withMessage('Valid scan ID is required')],
  async (req: Request, res: Response) => {
    const requestId = uuidv4();

    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid scan ID',
          details: errors.array(),
          requestId,
        });
      }

      const { scanId } = req.params;
      console.log(`🔍 [${requestId}] Retrieving GDPR scan result: ${scanId}`);

      const result = await GdprDatabase.getScanResult(scanId);

      if (!result) {
        console.warn(`⚠️ [${requestId}] GDPR scan not found: ${scanId}`);
        return res.status(404).json({
          success: false,
          error: 'NOT_FOUND',
          message: 'Scan result not found',
          requestId,
        });
      }

      console.log(`✅ [${requestId}] GDPR scan result retrieved successfully: ${scanId}`);

      res.json({
        success: true,
        data: result,
        requestId,
      });
    } catch (error) {
      console.error(`❌ [${requestId}] Failed to retrieve GDPR scan result:`, error);

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to retrieve scan result',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  },
);

/**
 * @openapi
 * /api/v1/compliance/gdpr/scans:
 *   get:
 *     summary: Get user's GDPR scan history
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of scans to retrieve
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of scans to skip
 *     responses:
 *       200:
 *         description: Scan history retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get(
  '/scans',
  // Temporarily remove Keycloak protection to fix dashboard data loading
  // keycloak.protect(),
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),

    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be 0 or greater'),
  ],
  async (req: AuthenticatedRequest, res: Response) => {
    const requestId = uuidv4();

    console.log(`🔥 [${requestId}] ===== GDPR SCAN HISTORY API CALLED =====`);
    console.log(`🔥 [${requestId}] Request Method: ${req.method}`);
    console.log(`🔥 [${requestId}] Request URL: ${req.originalUrl}`);
    console.log(`🔥 [${requestId}] Request Headers:`, JSON.stringify(req.headers, null, 2));
    console.log(`🔥 [${requestId}] Query Parameters:`, JSON.stringify(req.query, null, 2));

    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log(`❌ [${requestId}] Validation errors:`, errors.array());
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid query parameters',
          details: errors.array(),
          requestId,
        });
      }

      // Get authenticated user ID (with fallback to anonymous)
      console.log(`🔍 [${requestId}] Getting authenticated user ID...`);
      const userId = await getAuthenticatedUserId(req);
      const limit = parseInt(req.query.limit as string) || 10;
      const offset = parseInt(req.query.offset as string) || 0;

      console.log(`📋 [${requestId}] Processing scan history request:`);
      console.log(`📋 [${requestId}]   - User ID: ${userId}`);
      console.log(`📋 [${requestId}]   - Limit: ${limit}`);
      console.log(`📋 [${requestId}]   - Offset: ${offset}`);

      console.log(`💾 [${requestId}] Calling GdprDatabase.getUserScans...`);
      const scanEntities = await GdprDatabase.getUserScans(userId, limit, offset);

      console.log(
        `✅ [${requestId}] Retrieved ${scanEntities.length} GDPR scan entities for user: ${userId}`,
      );

      if (scanEntities.length > 0) {
        console.log(`📊 [${requestId}] First scan entity:`, {
          id: scanEntities[0].id,
          target_url: scanEntities[0].target_url,
          overall_score: scanEntities[0].overall_score,
          scan_timestamp: scanEntities[0].scan_timestamp,
        });
      }

      // Transform entities to proper GdprScanResult format
      console.log(
        `🔄 [${requestId}] Transforming ${scanEntities.length} entities to GdprScanResult format`,
      );

      const scans = await Promise.all(
        scanEntities.map(async (entity, index) => {
          console.log(
            `🔄 [${requestId}] Processing entity ${index + 1}/${scanEntities.length}: ${entity.id}`,
          );

          // Get check results for each scan to build complete result
          const checkResults = await GdprDatabase.getCheckResults(entity.id);
          console.log(
            `📊 [${requestId}] Found ${checkResults.length} check results for scan ${entity.id}`,
          );

          const result = GdprDatabase.mapEntityToResult(entity, checkResults);
          console.log(
            `✅ [${requestId}] Mapped scan ${entity.id} - Score: ${result.overallScore}%, URL: ${result.targetUrl}`,
          );

          return result;
        }),
      );

      console.log(`✅ [${requestId}] Successfully transformed ${scans.length} scans`);

      const responseData = {
        success: true,
        data: {
          scans,
          pagination: {
            limit,
            offset,
            total: scans.length,
          },
        },
        requestId,
      };

      console.log(`📤 [${requestId}] Sending scan history response:`);
      console.log(`📤 [${requestId}]   - Success: true`);
      console.log(`📤 [${requestId}]   - Scans count: ${scans.length}`);
      console.log(
        `📤 [${requestId}]   - First scan ID: ${scans.length > 0 ? scans[0].scanId : 'N/A'}`,
      );

      res.json(responseData);
    } catch (error) {
      console.error(`❌ [${requestId}] Failed to retrieve GDPR scan history:`, error);
      console.error(
        `❌ [${requestId}] Error stack:`,
        error instanceof Error ? error.stack : 'No stack trace',
      );

      const errorResponse = {
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to retrieve scan history',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      };

      console.log(
        `📤 [${requestId}] Sending error response:`,
        JSON.stringify(errorResponse, null, 2),
      );

      res.status(500).json(errorResponse);
    }
  },
);

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan/{scanId}/cookies:
 *   get:
 *     summary: Get detailed cookie analysis for a scan
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scanId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The scan ID to retrieve cookie analysis for
 *     responses:
 *       200:
 *         description: Cookie analysis retrieved successfully
 *       404:
 *         description: Scan not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/scan/:scanId/cookies',
  [param('scanId').isUUID().withMessage('Valid scan ID is required')],
  async (req: Request, res: Response) => {
    const requestId = uuidv4();

    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid scan ID',
          details: errors.array(),
          requestId,
        });
      }

      const { scanId } = req.params;
      console.log(`🍪 [${requestId}] Retrieving cookie analysis for scan: ${scanId}`);

      // Get REAL cookie analysis from database
      const cookieAnalysis = await GdprDatabase.getCookieAnalysis(scanId);

      console.log(
        `✅ [${requestId}] Retrieved ${cookieAnalysis.length} cookie records for scan: ${scanId}`,
      );

      res.json({
        success: true,
        data: {
          scanId,
          cookies: cookieAnalysis,
          summary: {
            totalCookies: cookieAnalysis.length,
            essentialCookies: cookieAnalysis.filter((c) => c.cookie_category === 'essential')
              .length,
            analyticsCookies: cookieAnalysis.filter((c) => c.cookie_category === 'analytics')
              .length,
            marketingCookies: cookieAnalysis.filter((c) => c.cookie_category === 'marketing')
              .length,
            functionalCookies: cookieAnalysis.filter((c) => c.cookie_category === 'functional')
              .length,
            thirdPartyCookies: cookieAnalysis.filter((c) => c.third_party).length,
            secureCookies: cookieAnalysis.filter((c) => c.secure_flag).length,
          },
        },
        requestId,
      });
    } catch (error) {
      console.error(`❌ [${requestId}] Failed to retrieve cookie analysis:`, error);

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to retrieve cookie analysis',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  },
);

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan/{scanId}/trackers:
 *   get:
 *     summary: Get detailed tracker analysis for a scan
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scanId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The scan ID to retrieve tracker analysis for
 *     responses:
 *       200:
 *         description: Tracker analysis retrieved successfully
 *       404:
 *         description: Scan not found
 *       500:
 *         description: Internal server error
 */
router.get(
  '/scan/:scanId/trackers',
  [param('scanId').isUUID().withMessage('Valid scan ID is required')],
  async (req: Request, res: Response) => {
    const requestId = uuidv4();

    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid scan ID',
          details: errors.array(),
          requestId,
        });
      }

      const { scanId } = req.params;
      console.log(`🔍 [${requestId}] Retrieving tracker analysis for scan: ${scanId}`);

      // Get REAL tracker analysis from database
      const trackerAnalysis = await GdprDatabase.getTrackerAnalysis(scanId);

      console.log(
        `✅ [${requestId}] Retrieved ${trackerAnalysis.length} tracker records for scan: ${scanId}`,
      );

      res.json({
        success: true,
        data: {
          scanId,
          trackers: trackerAnalysis,
          summary: {
            totalTrackers: trackerAnalysis.length,
            analyticsTrackers: trackerAnalysis.filter((t) => t.tracker_type === 'analytics').length,
            advertisingTrackers: trackerAnalysis.filter((t) => t.tracker_type === 'advertising')
              .length,
            socialTrackers: trackerAnalysis.filter((t) => t.tracker_type === 'social').length,
            functionalTrackers: trackerAnalysis.filter((t) => t.tracker_type === 'functional')
              .length,
            trackersBeforeConsent: trackerAnalysis.filter((t) => t.loads_before_consent).length,
            compliantTrackers: trackerAnalysis.filter((t) => t.compliant).length,
          },
        },
        requestId,
      });
    } catch (error) {
      console.error(`❌ [${requestId}] Failed to retrieve tracker analysis:`, error);

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to retrieve tracker analysis',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  },
);

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan/{scanId}/export:
 *   post:
 *     summary: Export GDPR scan report
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scanId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The scan ID to export
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - format
 *             properties:
 *               format:
 *                 type: string
 *                 enum: [json, csv, pdf]
 *                 description: Export format
 *     responses:
 *       200:
 *         description: Export generated successfully
 *       404:
 *         description: Scan not found
 *       500:
 *         description: Internal server error
 */
router.post(
  '/scan/:scanId/export',
  [
    param('scanId').isUUID().withMessage('Valid scan ID is required'),
    body('format').isIn(['json', 'csv', 'pdf']).withMessage('Format must be json, csv, or pdf'),
  ],
  async (req: Request, res: Response) => {
    const requestId = uuidv4();

    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid export parameters',
          details: errors.array(),
          requestId,
        });
      }

      const { scanId } = req.params;
      const { format } = req.body;

      console.log(`📄 [${requestId}] Exporting GDPR scan report: ${scanId} (${format})`);

      // Get REAL scan result for export
      const scanResult = await GdprDatabase.getScanResult(scanId);

      if (!scanResult) {
        return res.status(404).json({
          success: false,
          error: 'NOT_FOUND',
          message: 'Scan result not found',
          requestId,
        });
      }

      // Generate export data based on format
      let exportData: string | Buffer;
      let contentType: string;
      let filename: string;

      switch (format) {
        case 'json':
          exportData = JSON.stringify(scanResult, null, 2);
          contentType = 'application/json';
          filename = `gdpr-scan-${scanId}.json`;
          break;
        case 'csv': {
          // Basic CSV export - can be enhanced later
          const csvHeaders = 'Rule ID,Rule Name,Category,Passed,Score,Severity\n';
          const csvRows = scanResult.checks
            .map(
              (check) =>
                `"${check.ruleId}","${check.ruleName}","${check.category}",${check.passed},${check.score},"${check.severity}"`,
            )
            .join('\n');
          exportData = csvHeaders + csvRows;
          contentType = 'text/csv';
          filename = `gdpr-scan-${scanId}.csv`;
          break;
        }
        case 'pdf':
          // PDF export placeholder - would need PDF generation library
          exportData = 'PDF export functionality coming soon';
          contentType = 'text/plain';
          filename = `gdpr-scan-${scanId}.txt`;
          break;
        default:
          throw new Error('Unsupported export format');
      }

      console.log(`✅ [${requestId}] Export generated successfully: ${filename}`);

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.send(exportData);
    } catch (error) {
      console.error(`❌ [${requestId}] Failed to export GDPR scan:`, error);

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to export scan result',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      });
    }
  },
);

/**
 * @openapi
 * /api/v1/compliance/gdpr/scan/{scanId}/manual-review:
 *   put:
 *     summary: Update manual review status for a specific check
 *     tags: [GDPR Compliance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: scanId
 *         required: true
 *         schema:
 *           type: string
 *         description: The scan ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ruleId:
 *                 type: string
 *                 description: The rule ID for the manual review
 *               assessment:
 *                 type: string
 *                 enum: [compliant, non-compliant, partially-compliant, needs-review]
 *                 description: Legal assessment result
 *               notes:
 *                 type: string
 *                 description: Review notes
 *               reviewerName:
 *                 type: string
 *                 description: Name of the reviewer
 *             required:
 *               - ruleId
 *               - assessment
 *     responses:
 *       200:
 *         description: Manual review updated successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Scan not found
 *       500:
 *         description: Internal server error
 */
router.put(
  '/scan/:scanId/manual-review',
  keycloak.protect(),
  [
    param('scanId').isUUID().withMessage('Valid scan ID is required'),

    body('ruleId')
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage('Valid rule ID is required'),

    body('assessment')
      .isIn(['compliant', 'non-compliant', 'partially-compliant', 'needs-review'])
      .withMessage('Valid assessment is required'),

    body('notes')
      .optional()
      .isString()
      .isLength({ max: 2000 })
      .withMessage('Notes must be less than 2000 characters'),

    body('reviewerName')
      .optional()
      .isString()
      .isLength({ max: 100 })
      .withMessage('Reviewer name must be less than 100 characters'),
  ],
  async (req: AuthenticatedRequest, res: Response) => {
    const requestId = uuidv4();

    console.log(`🔥 [${requestId}] ===== MANUAL REVIEW API CALLED =====`);
    console.log(`🔥 [${requestId}] Request Method: ${req.method}`);
    console.log(`🔥 [${requestId}] Request URL: ${req.originalUrl}`);
    console.log(`🔥 [${requestId}] Request Headers:`, JSON.stringify(req.headers, null, 2));
    console.log(`🔥 [${requestId}] Request Body:`, JSON.stringify(req.body, null, 2));
    console.log(`🔥 [${requestId}] Request Params:`, JSON.stringify(req.params, null, 2));

    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log(`❌ [${requestId}] Validation errors:`, errors.array());
        return res.status(400).json({
          success: false,
          error: 'VALIDATION_ERROR',
          message: 'Invalid request parameters',
          details: errors.array(),
          requestId,
        });
      }

      const { scanId } = req.params;
      const { ruleId, assessment, notes, reviewerName } = req.body;
      const userId = await getAuthenticatedUserId(req);

      console.log(`📝 [${requestId}] Processing manual review update:`);
      console.log(`📝 [${requestId}]   - Scan ID: ${scanId}`);
      console.log(`📝 [${requestId}]   - Rule ID: ${ruleId}`);
      console.log(`📝 [${requestId}]   - Assessment: ${assessment}`);
      console.log(`📝 [${requestId}]   - Notes: ${notes || 'No notes'}`);
      console.log(`📝 [${requestId}]   - Reviewer: ${reviewerName || 'Unknown'}`);
      console.log(`📝 [${requestId}]   - User ID: ${userId}`);

      // Update manual review in database
      console.log(`💾 [${requestId}] Calling GdprDatabase.updateManualReview...`);
      const updated = await GdprDatabase.updateManualReview(scanId, ruleId, {
        assessment,
        notes: notes || '',
        reviewerName: reviewerName || 'Unknown',
        reviewedAt: new Date().toISOString(),
        reviewedBy: userId,
      });

      console.log(`💾 [${requestId}] Database update result: ${updated}`);

      if (!updated) {
        console.log(`⚠️ [${requestId}] No records updated - scan or rule not found`);
        return res.status(404).json({
          success: false,
          error: 'NOT_FOUND',
          message: 'Scan or manual review item not found',
          requestId,
        });
      }

      // Recalculate overall score
      console.log(`🔄 [${requestId}] Recalculating overall score...`);
      const updatedScan = await GdprDatabase.recalculateScore(scanId);
      console.log(`🔄 [${requestId}] Score recalculation result:`, updatedScan?.overallScore);

      console.log(`✅ [${requestId}] Manual review updated successfully`);
      console.log(`✅ [${requestId}] New overall score: ${updatedScan?.overallScore || 0}%`);

      const responseData = {
        success: true,
        data: {
          scanId,
          ruleId,
          assessment,
          updatedScore: updatedScan?.overallScore || 0,
          message: 'Manual review updated successfully',
        },
        requestId,
      };

      console.log(`📤 [${requestId}] Sending response:`, JSON.stringify(responseData, null, 2));

      res.json(responseData);
    } catch (error) {
      console.error(`❌ [${requestId}] Error updating manual review:`, error);
      console.error(
        `❌ [${requestId}] Error stack:`,
        error instanceof Error ? error.stack : 'No stack trace',
      );

      const errorResponse = {
        success: false,
        error: 'INTERNAL_ERROR',
        message: 'Failed to update manual review',
        details: error instanceof Error ? error.message : 'Unknown error',
        requestId,
      };

      console.log(
        `📤 [${requestId}] Sending error response:`,
        JSON.stringify(errorResponse, null, 2),
      );

      res.status(500).json(errorResponse);
    }
  },
);

export default router;
