/**
 * WCAG Test Page Component
 * Demonstration page showing all WCAG components working together
 */

'use client';

import React, { useState } from 'react';
import { WcagScanForm, WcagScanOverview, WcagScanProgress } from './index';
import { WcagScanFormData, WcagScanResult } from '../../types/wcag';
import wcagApiService from '../../services/wcag-api';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/Tabs';

const WcagTestPage: React.FC = () => {
  const [currentScan, setCurrentScan] = useState<WcagScanResult | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('form');

  /**
   * Handle scan form submission
   */
  const handleScanSubmit = async (formData: WcagScanFormData) => {
    try {
      setError('');
      setIsScanning(true);
      setActiveTab('progress');

      // Start the scan
      const scanResult = await wcagApiService.startScan(formData);
      setCurrentScan(scanResult);

      // If scan is already completed (unlikely but possible)
      if (scanResult.status === 'completed') {
        setIsScanning(false);
        setActiveTab('results');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start scan');
      setIsScanning(false);
      setActiveTab('form');
    }
  };

  /**
   * Handle scan completion
   */
  const handleScanComplete = async () => {
    if (currentScan) {
      try {
        // Fetch final scan results
        const finalResults = await wcagApiService.getScanDetails(currentScan.scanId);
        setCurrentScan(finalResults);
        setIsScanning(false);
        setActiveTab('results');
      } catch (err) {
        setError('Failed to fetch final scan results');
        setIsScanning(false);
      }
    }
  };

  /**
   * Handle scan cancellation
   */
  const handleScanCancel = () => {
    setCurrentScan(null);
    setIsScanning(false);
    setActiveTab('form');
  };

  /**
   * Create mock scan result for testing
   */
  const createMockScanResult = (): WcagScanResult => {
    return {
      scanId: 'test-scan-123',
      targetUrl: 'https://example.com',
      status: 'completed',
      overallScore: 85,
      levelAchieved: 'AA',
      riskLevel: 'medium',
      summary: {
        totalChecks: 21,
        passedChecks: 18,
        failedChecks: 3,
        automationRate: 0.87,
        categoryScores: {
          perceivable: 88,
          operable: 82,
          understandable: 90,
          robust: 80
        }
      },
      manualReviewSummary: {
        totalManualItems: 5,
        estimatedReviewTime: 15,
        priorityItems: 2
      },
      metadata: {
        scanId: 'test-scan-123',
        userId: 'test-user',
        requestId: 'test-request-123',
        startTime: new Date().toISOString(),
        duration: 45000,
        userAgent: 'Test Browser',
        viewport: { width: 1920, height: 1080 },
        environment: 'test',
        version: '1.0.0'
      },
      checks: [],
      manualReviewItems: []
    };
  };

  /**
   * Load mock data for testing
   */
  const loadMockData = () => {
    const mockResult = createMockScanResult();
    setCurrentScan(mockResult);
    setIsScanning(false);
    setActiveTab('results');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>WCAG Components Test Page</CardTitle>
          <p className="text-sm text-muted-foreground">
            Test and demonstrate WCAG compliance scanning components
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <button
              onClick={loadMockData}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Load Mock Results
            </button>
            <button
              onClick={() => {
                setCurrentScan(null);
                setIsScanning(false);
                setActiveTab('form');
                setError('');
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Reset
            </button>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="form">Scan Form</TabsTrigger>
              <TabsTrigger value="progress" disabled={!isScanning && !currentScan}>
                Progress
              </TabsTrigger>
              <TabsTrigger value="results" disabled={!currentScan || currentScan.status !== 'completed'}>
                Results
              </TabsTrigger>
            </TabsList>

            <TabsContent value="form" className="mt-6">
              <WcagScanForm
                onSubmit={handleScanSubmit}
                isLoading={isScanning}
                error={error}
              />
            </TabsContent>

            <TabsContent value="progress" className="mt-6">
              {currentScan && (
                <WcagScanProgress
                  scanId={currentScan.scanId}
                  onComplete={handleScanComplete}
                  onCancel={handleScanCancel}
                />
              )}
            </TabsContent>

            <TabsContent value="results" className="mt-6">
              {currentScan && currentScan.status === 'completed' && (
                <WcagScanOverview scanResult={currentScan} />
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default WcagTestPage;
