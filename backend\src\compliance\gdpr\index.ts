/**
 * GDPR Compliance Module - Main Entry Point
 *
 * This module provides comprehensive GDPR compliance scanning capabilities
 * including cookie analysis, consent mechanism testing, tracker detection,
 * and privacy policy analysis.
 *
 * @module GDPR
 */

// Export all types
export * from './types';

// Export core orchestrator
export { GdprOrchestrator } from './orchestrator';

// Export constants
export * from './constants';

// Export database layer
export { GdprDatabase } from './database/gdpr-database';

// Export services (will be added in later parts)
// export { CookieAnalysisService } from './services/cookie-analysis-service';
// export { ConsentTestingService } from './services/consent-testing-service';
// export { TrackerDetectionService } from './services/tracker-detection-service';
// export { PrivacyPolicyService } from './services/privacy-policy-service';

// Export checks
export * from './checks';

// Export utilities
// export * from './utils';

/**
 * GDPR Module Version
 */
export const GDPR_MODULE_VERSION = '1.0.0';

/**
 * Supported GDPR Rules
 */
export const SUPPORTED_GDPR_RULES = [
  'GDPR-001',
  'GDPR-002',
  'GDPR-003',
  'GDPR-004',
  'GDPR-005',
  'GDPR-006',
  'GDPR-007',
  'GDPR-008',
  'GDPR-009',
  'GDPR-010',
  'GDPR-011',
  'GDPR-012',
  'GDPR-013',
  'GDPR-014',
  'GDPR-015',
  'GDPR-016',
  'GDPR-017',
  'GDPR-018',
  'GDPR-019',
  'GDPR-020',
  'GDPR-021',
] as const;

/**
 * Simple wrapper functions for backward compatibility with scan service
 */
export async function checkCookieConsent(targetUrl: string) {
  const { CookieConsentCheck } = await import('./checks/cookie-consent');
  const check = new CookieConsentCheck();
  const result = await check.performCheck({ targetUrl, timeout: 30000 });

  // Transform to expected format for scan service
  return {
    checkId: result.ruleId,
    description: result.ruleName,
    passed: result.passed,
    score: result.score,
    evidence: result.evidence,
    recommendations: result.recommendations,
    details: {
      category: result.category,
      severity: result.severity,
      weight: result.weight,
      manualReviewRequired: result.manualReviewRequired,
    },
  };
}
