/**
 * GDPR Checks Only Test
 *
 * Test individual GDPR checks without database dependencies
 * to verify Part 3C implementation works correctly.
 */

describe('GDPR Part 3C - Individual Checks Test', () => {
  test('should import all 21 GDPR checks successfully', async () => {
    console.log('🔍 Testing GDPR check imports...');

    // Import all checks to verify they exist and can be instantiated
    const checks = await import('../checks');

    // Test core checks (Parts 1-3B)
    expect(checks.HttpsTlsCheck).toBeDefined();
    expect(checks.PrivacyPolicyCheck).toBeDefined();
    expect(checks.PrivacyContentCheck).toBeDefined();
    expect(checks.CookieConsentCheck).toBeDefined();
    expect(checks.CookieClassificationCheck).toBeDefined();
    expect(checks.TrackerDetectionCheck).toBeDefined();
    expect(checks.SecurityHeadersCheck).toBeDefined();
    expect(checks.IpAnonymizationCheck).toBeDefined();
    expect(checks.DataRightsCheck).toBeDefined();

    // Test new Part 3C checks
    expect(checks.CookieAttributesCheck).toBeDefined();
    expect(checks.GpcDntCheck).toBeDefined();
    expect(checks.FormConsentCheck).toBeDefined();
    expect(checks.SpecialDataCheck).toBeDefined();
    expect(checks.ChildrenConsentCheck).toBeDefined();
    expect(checks.DpoContactCheck).toBeDefined();
    expect(checks.DataTransfersCheck).toBeDefined();
    expect(checks.BreachNotificationCheck).toBeDefined();
    expect(checks.DpiaCheck).toBeDefined();
    expect(checks.DataRetentionCheck).toBeDefined();
    expect(checks.ProcessorAgreementsCheck).toBeDefined();
    expect(checks.ImprintContactCheck).toBeDefined();

    console.log('✅ All 21 GDPR checks imported successfully');
  });

  test('should instantiate all check classes', async () => {
    console.log('🔍 Testing GDPR check instantiation...');

    const checks = await import('../checks');

    // Test that all checks can be instantiated
    const checkInstances = [
      new checks.HttpsTlsCheck(),
      new checks.PrivacyPolicyCheck(),
      new checks.PrivacyContentCheck(),
      new checks.CookieConsentCheck(),
      new checks.CookieClassificationCheck(),
      new checks.TrackerDetectionCheck(),
      new checks.SecurityHeadersCheck(),
      new checks.IpAnonymizationCheck(),
      new checks.DataRightsCheck(),
      new checks.CookieAttributesCheck(),
      new checks.GpcDntCheck(),
      new checks.FormConsentCheck(),
      new checks.SpecialDataCheck(),
      new checks.ChildrenConsentCheck(),
      new checks.DpoContactCheck(),
      new checks.DataTransfersCheck(),
      new checks.BreachNotificationCheck(),
      new checks.DpiaCheck(),
      new checks.DataRetentionCheck(),
      new checks.ProcessorAgreementsCheck(),
      new checks.ImprintContactCheck(),
    ];

    expect(checkInstances.length).toBe(21);

    // Verify each instance has the performCheck method
    for (const instance of checkInstances) {
      expect(typeof instance.performCheck).toBe('function');
    }

    console.log('✅ All 21 GDPR check classes instantiated successfully');
  });

  test('should execute individual Part 3C checks', async () => {
    console.log('🔍 Testing individual Part 3C check execution...');

    const checks = await import('../checks');
    const config = {
      targetUrl: 'https://example.com',
      timeout: 10000,
    };

    // Test Cookie Attributes Check (GDPR-007)
    const cookieAttributesCheck = new checks.CookieAttributesCheck();
    const cookieResult = await cookieAttributesCheck.performCheck(config);

    expect(cookieResult.ruleId).toBe('GDPR-007');
    expect(cookieResult.ruleName).toBe('Cookie Security Attributes');
    expect(cookieResult.category).toBe('cookies');
    expect(typeof cookieResult.passed).toBe('boolean');
    expect(typeof cookieResult.score).toBe('number');
    expect(cookieResult.weight).toBe(5);
    expect(cookieResult.severity).toBe('medium');
    expect(Array.isArray(cookieResult.evidence)).toBe(true);
    expect(Array.isArray(cookieResult.recommendations)).toBe(true);
    expect(typeof cookieResult.manualReviewRequired).toBe('boolean');

    console.log(`   ✅ GDPR-007 Cookie Attributes: Score ${cookieResult.score}%`);

    // Test GPC/DNT Check (GDPR-008)
    const gpcCheck = new checks.GpcDntCheck();
    const gpcResult = await gpcCheck.performCheck(config);

    expect(gpcResult.ruleId).toBe('GDPR-008');
    expect(gpcResult.ruleName).toBe('Global Privacy Control/Do-Not-Track');
    expect(gpcResult.category).toBe('consent');

    console.log(`   ✅ GDPR-008 GPC/DNT: Score ${gpcResult.score}%`);

    // Test Form Consent Check (GDPR-009)
    const formCheck = new checks.FormConsentCheck();
    const formResult = await formCheck.performCheck(config);

    expect(formResult.ruleId).toBe('GDPR-009');
    expect(formResult.ruleName).toBe('Data-Collecting Forms & Consent Controls');
    expect(formResult.category).toBe('consent');

    console.log(`   ✅ GDPR-009 Form Consent: Score ${formResult.score}%`);

    // Test DPO Contact Check (GDPR-015)
    const dpoCheck = new checks.DpoContactCheck();
    const dpoResult = await dpoCheck.performCheck(config);

    expect(dpoResult.ruleId).toBe('GDPR-015');
    expect(dpoResult.ruleName).toBe('Data Protection Officer/EU Representative');
    expect(dpoResult.category).toBe('organizational');

    console.log(`   ✅ GDPR-015 DPO Contact: Score ${dpoResult.score}%`);

    console.log('✅ Individual Part 3C checks executed successfully');
  }, 60000); // 60 second timeout

  test('should handle manual review checks correctly', async () => {
    console.log('🔍 Testing manual review checks...');

    const checks = await import('../checks');
    const config = {
      targetUrl: 'https://example.com',
      timeout: 5000,
    };

    // Test Special Data Check (GDPR-013) - Should require manual review
    const specialDataCheck = new checks.SpecialDataCheck();
    const specialResult = await specialDataCheck.performCheck(config);

    expect(specialResult.ruleId).toBe('GDPR-013');
    expect(specialResult.manualReviewRequired).toBe(true);
    expect(specialResult.severity).toBe('high');

    console.log(
      `   ✅ GDPR-013 Special Data: Manual Review = ${specialResult.manualReviewRequired}`,
    );

    // Test Children's Consent Check (GDPR-014) - Should require manual review
    const childrenCheck = new checks.ChildrenConsentCheck();
    const childrenResult = await childrenCheck.performCheck(config);

    expect(childrenResult.ruleId).toBe('GDPR-014');
    expect(childrenResult.manualReviewRequired).toBe(true);
    expect(childrenResult.weight).toBe(0); // No weight in automated scoring

    console.log(
      `   ✅ GDPR-014 Children Consent: Manual Review = ${childrenResult.manualReviewRequired}`,
    );

    // Test Data Transfers Check (GDPR-016) - Should require manual review
    const transfersCheck = new checks.DataTransfersCheck();
    const transfersResult = await transfersCheck.performCheck(config);

    expect(transfersResult.ruleId).toBe('GDPR-016');
    expect(transfersResult.manualReviewRequired).toBe(true);

    console.log(
      `   ✅ GDPR-016 Data Transfers: Manual Review = ${transfersResult.manualReviewRequired}`,
    );

    // Test DPIA Check (GDPR-018) - Should require manual review
    const dpiaCheck = new checks.DpiaCheck();
    const dpiaResult = await dpiaCheck.performCheck(config);

    expect(dpiaResult.ruleId).toBe('GDPR-018');
    expect(dpiaResult.manualReviewRequired).toBe(true);
    expect(dpiaResult.weight).toBe(0); // No weight in automated scoring

    console.log(`   ✅ GDPR-018 DPIA: Manual Review = ${dpiaResult.manualReviewRequired}`);

    console.log('✅ Manual review checks verified successfully');
  }, 45000);

  test('should verify check result structure consistency', async () => {
    console.log('🔍 Testing check result structure consistency...');

    const checks = await import('../checks');
    const config = {
      targetUrl: 'https://example.com',
      timeout: 5000,
    };

    // Test a few different checks to ensure consistent structure
    const testChecks = [
      { check: new checks.DataRetentionCheck(), expectedRuleId: 'GDPR-019' },
      { check: new checks.ProcessorAgreementsCheck(), expectedRuleId: 'GDPR-020' },
      { check: new checks.ImprintContactCheck(), expectedRuleId: 'GDPR-021' },
    ];

    for (const { check, expectedRuleId } of testChecks) {
      const result = await check.performCheck(config);

      // Verify required properties
      expect(result.ruleId).toBe(expectedRuleId);
      expect(typeof result.ruleName).toBe('string');
      expect(typeof result.category).toBe('string');
      expect(typeof result.passed).toBe('boolean');
      expect(typeof result.score).toBe('number');
      expect(typeof result.weight).toBe('number');
      expect(typeof result.severity).toBe('string');
      expect(Array.isArray(result.evidence)).toBe(true);
      expect(Array.isArray(result.recommendations)).toBe(true);
      expect(typeof result.manualReviewRequired).toBe('boolean');

      // Verify score is within valid range
      expect(result.score).toBeGreaterThanOrEqual(0);
      expect(result.score).toBeLessThanOrEqual(100);

      // Verify severity is valid
      expect(['critical', 'high', 'medium', 'low']).toContain(result.severity);

      console.log(`   ✅ ${expectedRuleId}: Structure verified`);
    }

    console.log('✅ Check result structure consistency verified');
  }, 30000);
});
