// Known tracker domains and their classifications
export const TRACKER_DATABASE = {
  // Analytics Trackers
  analytics: [
    {
      domain: 'google-analytics.com',
      name: 'Google Analytics',
      dataTypes: ['pageviews', 'user_behavior', 'demographics'],
    },
    {
      domain: 'googletagmanager.com',
      name: 'Google Tag Manager',
      dataTypes: ['tag_management', 'event_tracking'],
    },
    {
      domain: 'hotjar.com',
      name: 'Hotjar',
      dataTypes: ['heatmaps', 'session_recordings', 'user_feedback'],
    },
    {
      domain: 'adobe.com',
      name: 'Adobe Analytics',
      dataTypes: ['web_analytics', 'customer_journey'],
    },
    { domain: 'mixpanel.com', name: 'Mixpanel', dataTypes: ['event_analytics', 'user_behavior'] },
    {
      domain: 'amplitude.com',
      name: 'Amplitude',
      dataTypes: ['product_analytics', 'user_retention'],
    },
  ],

  // Advertising Trackers
  advertising: [
    {
      domain: 'doubleclick.net',
      name: 'Google DoubleClick',
      dataTypes: ['ad_targeting', 'conversion_tracking'],
    },
    {
      domain: 'facebook.com',
      name: 'Facebook Pixel',
      dataTypes: ['ad_targeting', 'conversion_tracking', 'audience_building'],
    },
    {
      domain: 'twitter.com',
      name: 'Twitter Analytics',
      dataTypes: ['ad_performance', 'audience_insights'],
    },
    {
      domain: 'linkedin.com',
      name: 'LinkedIn Insight Tag',
      dataTypes: ['b2b_targeting', 'conversion_tracking'],
    },
    {
      domain: 'adsystem.amazon.com',
      name: 'Amazon DSP',
      dataTypes: ['product_advertising', 'audience_targeting'],
    },
    {
      domain: 'adnxs.com',
      name: 'AppNexus',
      dataTypes: ['programmatic_advertising', 'real_time_bidding'],
    },
  ],

  // Social Media Trackers
  social: [
    { domain: 'youtube.com', name: 'YouTube', dataTypes: ['video_analytics', 'user_engagement'] },
    {
      domain: 'instagram.com',
      name: 'Instagram',
      dataTypes: ['social_engagement', 'content_performance'],
    },
    { domain: 'tiktok.com', name: 'TikTok Pixel', dataTypes: ['video_engagement', 'ad_targeting'] },
    {
      domain: 'pinterest.com',
      name: 'Pinterest Tag',
      dataTypes: ['content_discovery', 'shopping_behavior'],
    },
  ],

  // Functional Trackers
  functional: [
    {
      domain: 'zendesk.com',
      name: 'Zendesk',
      dataTypes: ['customer_support', 'chat_interactions'],
    },
    {
      domain: 'intercom.io',
      name: 'Intercom',
      dataTypes: ['customer_messaging', 'user_onboarding'],
    },
    {
      domain: 'drift.com',
      name: 'Drift',
      dataTypes: ['conversational_marketing', 'lead_qualification'],
    },
    {
      domain: 'hubspot.com',
      name: 'HubSpot',
      dataTypes: ['crm_integration', 'marketing_automation'],
    },
  ],
} as const;

export class TrackerDatabase {
  /**
   * Classify tracker by domain
   */
  static classifyTracker(domain: string): {
    category: 'analytics' | 'advertising' | 'social' | 'functional' | 'unknown';
    name: string;
    dataTypes: string[];
    riskLevel: 'critical' | 'high' | 'medium' | 'low';
  } {
    // Check each category
    for (const [category, trackers] of Object.entries(TRACKER_DATABASE)) {
      for (const tracker of trackers) {
        if (domain.includes(tracker.domain) || tracker.domain.includes(domain)) {
          return {
            category: category as 'analytics' | 'advertising' | 'social' | 'functional',
            name: tracker.name,
            dataTypes: [...tracker.dataTypes],
            riskLevel: this.determineRiskLevel(
              category as 'analytics' | 'advertising' | 'social' | 'functional',
              [...tracker.dataTypes],
            ),
          };
        }
      }
    }

    return {
      category: 'unknown',
      name: 'Unknown Tracker',
      dataTypes: ['unknown'],
      riskLevel: 'medium',
    };
  }

  /**
   * Determine risk level based on tracker type and data collection
   */
  private static determineRiskLevel(
    category: string,
    dataTypes: string[],
  ): 'critical' | 'high' | 'medium' | 'low' {
    // High-risk data types
    const highRiskTypes = [
      'conversion_tracking',
      'audience_building',
      'ad_targeting',
      'session_recordings',
    ];

    if (category === 'advertising' && dataTypes.some((type) => highRiskTypes.includes(type))) {
      return 'critical';
    }

    if (category === 'advertising' || dataTypes.some((type) => highRiskTypes.includes(type))) {
      return 'high';
    }

    if (category === 'analytics' || category === 'social') {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Get all known tracker domains
   */
  static getAllTrackerDomains(): string[] {
    const domains: string[] = [];

    for (const trackers of Object.values(TRACKER_DATABASE)) {
      for (const tracker of trackers) {
        domains.push(tracker.domain);
      }
    }

    return domains;
  }
}
