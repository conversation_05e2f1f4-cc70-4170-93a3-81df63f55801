import { GdprCheckResult, Evidence } from '../types';

export interface SecurityHeadersCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class SecurityHeadersCheck {
  /**
   * Check security headers for privacy by design
   * REAL ANALYSIS - checks actual HTTP headers
   */
  async performCheck(config: SecurityHeadersCheckConfig): Promise<GdprCheckResult> {
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      const response = await fetch(config.targetUrl, {
        method: 'HEAD',
        redirect: 'follow',
      });

      const headers = response.headers;

      // Check Content Security Policy
      const csp = headers.get('content-security-policy');
      if (csp) {
        score += 25;
        evidence.push({
          type: 'network',
          description: 'Content Security Policy header found',
          value: csp.substring(0, 100) + '...',
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'Content Security Policy header missing',
          value: 'Recommended for data protection',
        });
      }

      // Check X-Frame-Options
      const xFrame = headers.get('x-frame-options');
      if (xFrame) {
        score += 20;
        evidence.push({
          type: 'network',
          description: 'X-Frame-Options header found',
          value: xFrame,
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'X-Frame-Options header missing',
          value: 'Prevents clickjacking attacks',
        });
      }

      // Check X-Content-Type-Options
      const xContentType = headers.get('x-content-type-options');
      if (xContentType === 'nosniff') {
        score += 15;
        evidence.push({
          type: 'network',
          description: 'X-Content-Type-Options header properly set',
          value: xContentType,
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'X-Content-Type-Options header missing or incorrect',
          value: 'Should be set to "nosniff"',
        });
      }

      // Check Referrer Policy
      const referrerPolicy = headers.get('referrer-policy');
      if (referrerPolicy) {
        score += 20;
        evidence.push({
          type: 'network',
          description: 'Referrer-Policy header found',
          value: referrerPolicy,
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'Referrer-Policy header missing',
          value: 'Controls referrer information sharing',
        });
      }

      // Check Permissions Policy
      const permissionsPolicy = headers.get('permissions-policy');
      if (permissionsPolicy) {
        score += 20;
        evidence.push({
          type: 'network',
          description: 'Permissions-Policy header found',
          value: permissionsPolicy.substring(0, 100) + '...',
        });
      } else {
        evidence.push({
          type: 'network',
          description: 'Permissions-Policy header missing',
          value: 'Controls browser feature access',
        });
      }

      const passed = score >= 60; // 60% threshold

      return {
        ruleId: 'GDPR-010',
        ruleName: 'Security Headers (Privacy by Design)',
        category: 'security',
        passed,
        score,
        weight: 5,
        severity: 'medium',
        evidence,
        recommendations: passed
          ? []
          : [
              {
                priority: 1,
                title: 'Implement security headers',
                description: 'Add missing security headers for privacy by design',
                implementation:
                  'Configure web server with CSP, X-Frame-Options, and other security headers',
                effort: 'moderate',
                impact: 'medium',
              },
            ],
        manualReviewRequired: false,
      };
    } catch (error) {
      return {
        ruleId: 'GDPR-010',
        ruleName: 'Security Headers (Privacy by Design)',
        category: 'security',
        passed: false,
        score: 0,
        weight: 5,
        severity: 'medium',
        evidence: [
          {
            type: 'text',
            description: 'Security headers check failed',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: 'Fix website accessibility for security analysis',
            description: 'Ensure website is accessible for security header analysis',
            implementation: 'Check website loading and network accessibility',
            effort: 'minimal',
            impact: 'medium',
          },
        ],
        manualReviewRequired: false,
      };
    }
  }
}
