import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // GDPR Scans Table
  await knex.schema.createTable('gdpr_scans', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('target_url', 2048).notNullable();
    table.timestamp('scan_timestamp', { useTz: true }).defaultTo(knex.fn.now());
    table.integer('scan_duration'); // milliseconds
    table.decimal('overall_score', 5, 2);
    table.enu('risk_level', ['critical', 'high', 'medium', 'low']);
    table.integer('total_checks').notNullable();
    table.integer('passed_checks').notNullable();
    table.integer('failed_checks').notNullable();
    table.integer('manual_review_required').defaultTo(0);
    table.enu('scan_status', ['pending', 'running', 'completed', 'failed']).defaultTo('pending');
    table.text('error_message');
    table.jsonb('metadata'); // scan configuration and technical details
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('updated_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('user_id');
    table.index('scan_timestamp');
    table.index('scan_status');
    table.index('target_url');
  });

  // GDPR Check Results Table
  await knex.schema.createTable('gdpr_check_results', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('rule_id', 50).notNullable(); // e.g., 'GDPR-001', 'GDPR-002'
    table.string('rule_name', 200).notNullable();
    table.string('category', 100).notNullable(); // e.g., 'consent', 'privacy_policy', 'cookies'
    table.boolean('passed').notNullable();
    table.decimal('score', 5, 2);
    table.decimal('weight', 3, 2);
    table.enu('severity', ['critical', 'high', 'medium', 'low']);
    table.boolean('manual_review_required').defaultTo(false);
    table.jsonb('evidence'); // detailed findings and evidence
    table.jsonb('recommendations'); // improvement suggestions
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('rule_id');
    table.index('category');
    table.index('passed');
  });

  // GDPR Cookie Analysis Table
  await knex.schema.createTable('gdpr_cookie_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('cookie_name', 255).notNullable();
    table.string('cookie_domain', 255).notNullable();
    table.string('cookie_category', 50); // essential, analytics, marketing, etc.
    table.boolean('has_consent');
    table.boolean('secure_flag');
    table.boolean('httponly_flag');
    table.string('samesite_attribute', 20);
    table.timestamp('expiry_date', { useTz: true });
    table.text('purpose');
    table.boolean('third_party').defaultTo(false);
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('cookie_category');
    table.index('third_party');
  });

  // GDPR Consent Analysis Table
  await knex.schema.createTable('gdpr_consent_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('consent_type', 50); // banner, form, checkbox
    table.string('consent_mechanism', 100); // opt-in, opt-out, granular
    table.boolean('has_reject_option');
    table.boolean('has_granular_options');
    table.boolean('pre_ticked_boxes');
    table.text('consent_text');
    table.boolean('privacy_policy_linked');
    table.boolean('compliant');
    table.jsonb('issues'); // array of compliance issues
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('consent_type');
    table.index('compliant');
  });

  // GDPR Tracker Analysis Table
  await knex.schema.createTable('gdpr_tracker_analysis', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('scan_id').notNullable().references('id').inTable('gdpr_scans').onDelete('CASCADE');
    table.string('tracker_domain', 255).notNullable();
    table.string('tracker_type', 100); // analytics, advertising, social, etc.
    table.string('tracker_name', 255);
    table.boolean('loads_before_consent');
    table.boolean('has_consent_mechanism');
    table.text('data_transferred'); // description of data being transferred
    table.boolean('privacy_policy_mentioned');
    table.boolean('compliant');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());

    // Indexes
    table.index('scan_id');
    table.index('tracker_type');
    table.index('compliant');
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists('gdpr_tracker_analysis');
  await knex.schema.dropTableIfExists('gdpr_consent_analysis');
  await knex.schema.dropTableIfExists('gdpr_cookie_analysis');
  await knex.schema.dropTableIfExists('gdpr_check_results');
  await knex.schema.dropTableIfExists('gdpr_scans');
}