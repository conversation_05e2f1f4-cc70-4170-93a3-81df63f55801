# GDPR Implementation Plan - Part 05: API Integration and Routes

## Overview
This document implements the GDPR API endpoints, route integration, and ScanService integration. All endpoints handle real scan data - NO MOCK RESPONSES.

## ⚠️ CRITICAL REQUIREMENTS
- **Real Scan Data Only**: All API endpoints must return actual scan results
- **No Mock Responses**: Prohibited - use genuine database data and live scanning
- **Zod Validation**: All request bodies must be validated with Zod schemas
- **Proper Error Handling**: Follow .projectrules for error handling patterns

## Step 1: GDPR API Routes

### 1.1 Create GDPR Routes File
Create `backend/src/routes/compliance/gdpr.ts`:

```typescript
import { Router } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { GdprOrchestrator } from '../../compliance/gdpr/orchestrator';
import { GdprDatabase } from '../../compliance/gdpr/database/gdpr-database';
import { GdprScanRequestSchema } from '../../compliance/gdpr/types';
import { ApiResponse } from '../../types';

const router = Router();

/**
 * @openapi
 * tags:
 *   name: GDPR Compliance
 *   description: Routes for GDPR compliance scanning and analysis
 */

/**
 * Start GDPR compliance scan
 * REAL SCANNING ONLY - initiates actual website analysis
 */
router.post(
  '/scan',
  [
    body('targetUrl')
      .isURL({ require_protocol: true, protocols: ['http', 'https'] })
      .withMessage('Valid URL is required'),
    body('scanOptions.enableCookieAnalysis')
      .optional()
      .isBoolean()
      .withMessage('enableCookieAnalysis must be boolean'),
    body('scanOptions.enableTrackerDetection')
      .optional()
      .isBoolean()
      .withMessage('enableTrackerDetection must be boolean'),
    body('scanOptions.enableConsentTesting')
      .optional()
      .isBoolean()
      .withMessage('enableConsentTesting must be boolean'),
    body('scanOptions.maxPages')
      .optional()
      .isInt({ min: 1, max: 50 })
      .withMessage('maxPages must be between 1 and 50'),
    body('scanOptions.timeout')
      .optional()
      .isInt({ min: 60000, max: 3600000 })
      .withMessage('timeout must be between 1 minute and 1 hour'),
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request parameters',
            details: errors.array(),
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(400).json(response);
      }

      // Parse and validate with Zod
      const parseResult = GdprScanRequestSchema.safeParse(req.body);
      if (!parseResult.success) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Request validation failed',
            details: parseResult.error.errors,
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(400).json(response);
      }

      const scanRequest = parseResult.data;
      
      // Get user ID from authentication
      const userId = (req as any).user?.id;
      if (!userId) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User authentication required',
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(401).json(response);
      }

      console.log(`🔍 Starting GDPR scan for user ${userId}: ${scanRequest.targetUrl}`);

      // Initialize orchestrator and start REAL scan
      const orchestrator = new GdprOrchestrator();
      const scanResult = await orchestrator.performComprehensiveScan(userId, scanRequest);

      console.log(`✅ GDPR scan completed: ${scanResult.scanId}`);

      const response: ApiResponse<typeof scanResult> = {
        success: true,
        data: scanResult,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ GDPR scan failed:', error);
      
      const response: ApiResponse<null> = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'GDPR scan failed',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(500).json(response);
    }
  }
);

/**
 * Get GDPR scan result by ID
 * REAL DATA ONLY - returns actual scan results from database
 */
router.get(
  '/scan/:scanId',
  [
    param('scanId')
      .isUUID()
      .withMessage('Valid scan ID is required'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid scan ID',
            details: errors.array(),
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(400).json(response);
      }

      const { scanId } = req.params;
      
      // Retrieve REAL scan result from database
      const scanResult = await GdprDatabase.getScanResult(scanId);
      
      if (!scanResult) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Scan result not found',
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(404).json(response);
      }

      const response: ApiResponse<typeof scanResult> = {
        success: true,
        data: scanResult,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan result:', error);
      
      const response: ApiResponse<null> = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve scan result',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(500).json(response);
    }
  }
);

/**
 * Get user's GDPR scan history
 * REAL DATA ONLY - returns actual user scan history
 */
router.get(
  '/scans',
  [
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('offset')
      .optional()
      .isInt({ min: 0 })
      .withMessage('Offset must be non-negative'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid query parameters',
            details: errors.array(),
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(400).json(response);
      }

      const userId = (req as any).user?.id;
      if (!userId) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User authentication required',
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(401).json(response);
      }

      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      // Get REAL scan history from database
      const scans = await GdprDatabase.getUserScans(userId, limit, offset);

      const response: ApiResponse<typeof scans> = {
        success: true,
        data: scans,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to retrieve GDPR scan history:', error);
      
      const response: ApiResponse<null> = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve scan history',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(500).json(response);
    }
  }
);

/**
 * Get detailed cookie analysis for a scan
 * REAL DATA ONLY - returns actual cookie analysis from database
 */
router.get(
  '/scan/:scanId/cookies',
  [
    param('scanId')
      .isUUID()
      .withMessage('Valid scan ID is required'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid scan ID',
            details: errors.array(),
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(400).json(response);
      }

      const { scanId } = req.params;

      // Get REAL cookie analysis from database
      const cookieAnalysis = await GdprDatabase.getCookieAnalysis(scanId);

      const response: ApiResponse<typeof cookieAnalysis> = {
        success: true,
        data: cookieAnalysis,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(200).json(response);

    } catch (error) {
      console.error('❌ Failed to retrieve cookie analysis:', error);
      
      const response: ApiResponse<null> = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve cookie analysis',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(500).json(response);
    }
  }
);

/**
 * Export GDPR scan report
 * REAL DATA ONLY - exports actual scan results
 */
router.post(
  '/scan/:scanId/export',
  [
    param('scanId')
      .isUUID()
      .withMessage('Valid scan ID is required'),
    body('format')
      .isIn(['pdf', 'json', 'csv'])
      .withMessage('Format must be pdf, json, or csv'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid export parameters',
            details: errors.array(),
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(400).json(response);
      }

      const { scanId } = req.params;
      const { format } = req.body;

      // Get REAL scan result for export
      const scanResult = await GdprDatabase.getScanResult(scanId);
      
      if (!scanResult) {
        const response: ApiResponse<null> = {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Scan result not found',
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string || 'unknown',
          },
        };
        return res.status(404).json(response);
      }

      // TODO: Implement actual export functionality
      // For now, return the scan data in requested format
      let exportData: string | Buffer;
      let contentType: string;
      let filename: string;

      switch (format) {
        case 'json':
          exportData = JSON.stringify(scanResult, null, 2);
          contentType = 'application/json';
          filename = `gdpr-scan-${scanId}.json`;
          break;
        case 'csv':
          // TODO: Implement CSV export
          exportData = 'CSV export not yet implemented';
          contentType = 'text/csv';
          filename = `gdpr-scan-${scanId}.csv`;
          break;
        case 'pdf':
          // TODO: Implement PDF export
          exportData = 'PDF export not yet implemented';
          contentType = 'application/pdf';
          filename = `gdpr-scan-${scanId}.pdf`;
          break;
        default:
          throw new Error('Unsupported export format');
      }

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.send(exportData);

    } catch (error) {
      console.error('❌ Failed to export GDPR scan:', error);
      
      const response: ApiResponse<null> = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to export scan result',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      };

      res.status(500).json(response);
    }
  }
);

export default router;
```

## Step 2: ScanService Integration

### 2.1 Update ScanService for GDPR
Edit `backend/src/services/scan-service.ts` to add GDPR support:

```typescript
// Add to existing ScanService class

import { GdprOrchestrator } from '../compliance/gdpr/orchestrator';
import { GdprScanRequest, GdprScanResult } from '../compliance/gdpr/types';

export class ScanService {
  // ... existing methods ...

  /**
   * Perform GDPR compliance scan
   * REAL SCANNING ONLY - no mock data
   */
  async performGdprScan(
    userId: string,
    scanRequest: GdprScanRequest
  ): Promise<GdprScanResult> {
    console.log(`🔍 ScanService: Starting GDPR scan for ${scanRequest.targetUrl}`);
    
    try {
      const orchestrator = new GdprOrchestrator();
      const result = await orchestrator.performComprehensiveScan(userId, scanRequest);
      
      console.log(`✅ ScanService: GDPR scan completed successfully`);
      return result;
      
    } catch (error) {
      console.error(`❌ ScanService: GDPR scan failed:`, error);
      throw new Error(`GDPR scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get supported scan types including GDPR
   */
  getSupportedScanTypes(): string[] {
    return ['hipaa-privacy', 'hipaa-security', 'gdpr', 'wcag', 'ada'];
  }
}
```

## Step 3: Route Integration

### 3.1 Update Main Compliance Router
Edit `backend/src/routes/compliance/index.ts`:

```typescript
import express, { Router } from 'express';
import scanRoutes from './scan';
import adaRoutes from './ada';
import hipaaRoutes from './hipaa';
import gdprRoutes from './gdpr'; // Add GDPR routes

const router: Router = express.Router();

router.use('/scans', scanRoutes);
router.use('/ada', adaRoutes);
router.use('/hipaa', hipaaRoutes);
router.use('/gdpr', gdprRoutes); // Mount GDPR routes

export default router;
```

### 3.2 Update Main App Integration
Edit `backend/src/index.ts` to include GDPR routes:

```typescript
// Add to existing imports
import gdprRoutes from './routes/compliance/gdpr';

// Add to existing route mounting
app.use('/api/v1/compliance/gdpr', gdprRoutes);

// Update features list in root route
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'Comply Checker Backend is alive!',
    version: process.env.npm_package_version || '1.0.0',
    environment: env.NODE_ENV,
    uptime: process.uptime(),
    features: [
      'HIPAA Compliance Analysis', 
      '3-Level Analysis System',
      'GDPR Compliance Scanning', // Add GDPR feature
      'Cookie Consent Analysis',
      'Tracker Detection'
    ],
    endpoints: {
      health: '/health',
      scan: '/api/v1/compliance/scan',
      scans: '/api/v1/compliance/scans',
      gdpr: '/api/v1/compliance/gdpr', // Add GDPR endpoint
    },
  });
});
```

## Step 4: Database Method Extensions

### 4.1 Add Missing Database Methods
Add to `backend/src/compliance/gdpr/database/gdpr-database.ts`:

```typescript
// Add these methods to the existing GdprDatabase class

/**
 * Get cookie analysis for a scan
 */
static async getCookieAnalysis(scanId: string): Promise<Array<{
  id: string;
  scan_id: string;
  cookie_name: string;
  cookie_domain: string;
  cookie_category: string;
  has_consent: boolean;
  secure_flag: boolean;
  httponly_flag: boolean;
  samesite_attribute?: string;
  expiry_date?: Date;
  purpose?: string;
  third_party: boolean;
  created_at: Date;
}>> {
  try {
    return await knex('gdpr_cookie_analysis')
      .where('scan_id', scanId)
      .orderBy('created_at', 'asc');
  } catch (error) {
    console.error('❌ Failed to retrieve cookie analysis:', error);
    throw new Error(`Cookie analysis retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get consent analysis for a scan
 */
static async getConsentAnalysis(scanId: string): Promise<Array<{
  id: string;
  scan_id: string;
  consent_type: string;
  consent_mechanism: string;
  has_reject_option: boolean;
  has_granular_options: boolean;
  pre_ticked_boxes: boolean;
  consent_text: string;
  privacy_policy_linked: boolean;
  compliant: boolean;
  issues: string[];
  created_at: Date;
}>> {
  try {
    return await knex('gdpr_consent_analysis')
      .where('scan_id', scanId)
      .orderBy('created_at', 'asc');
  } catch (error) {
    console.error('❌ Failed to retrieve consent analysis:', error);
    throw new Error(`Consent analysis retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get tracker analysis for a scan
 */
static async getTrackerAnalysis(scanId: string): Promise<Array<{
  id: string;
  scan_id: string;
  tracker_domain: string;
  tracker_type: string;
  tracker_name: string;
  loads_before_consent: boolean;
  has_consent_mechanism: boolean;
  data_transferred: string;
  privacy_policy_mentioned: boolean;
  compliant: boolean;
  created_at: Date;
}>> {
  try {
    return await knex('gdpr_tracker_analysis')
      .where('scan_id', scanId)
      .orderBy('created_at', 'asc');
  } catch (error) {
    console.error('❌ Failed to retrieve tracker analysis:', error);
    throw new Error(`Tracker analysis retrieval failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Delete scan and all related data
 */
static async deleteScan(scanId: string): Promise<void> {
  try {
    await knex.transaction(async (trx) => {
      // Delete related data first (foreign key constraints)
      await trx('gdpr_tracker_analysis').where('scan_id', scanId).del();
      await trx('gdpr_consent_analysis').where('scan_id', scanId).del();
      await trx('gdpr_cookie_analysis').where('scan_id', scanId).del();
      await trx('gdpr_check_results').where('scan_id', scanId).del();
      
      // Delete main scan record
      await trx('gdpr_scans').where('id', scanId).del();
    });
  } catch (error) {
    console.error('❌ Failed to delete GDPR scan:', error);
    throw new Error(`Scan deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
```

## Next Steps
Continue with Part 06: Frontend Components and Integration to complete the GDPR implementation.

## Validation Checklist
- [ ] All API endpoints return real scan data (no mock responses)
- [ ] Zod validation implemented for all request bodies
- [ ] Proper error handling following .projectrules
- [ ] ScanService integration completed
- [ ] Route mounting in main application
- [ ] Database methods for all GDPR data retrieval
- [ ] TypeScript strict compliance (no `any` types)
- [ ] Ready for frontend integration
