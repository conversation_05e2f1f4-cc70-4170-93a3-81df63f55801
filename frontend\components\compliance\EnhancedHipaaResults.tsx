import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { EnhancedHipaaResults } from '@/types/hipaa';

interface EnhancedHipaaResultsComponentProps {
  enhancedHipaaResults: EnhancedHipaaResults;
}

export default function EnhancedHipaaResultsComponent({
  enhancedHipaaResults,
}: EnhancedHipaaResultsComponentProps) {
  const { overallScore, summary, checksBreakdown, checks, recommendations, metadata } =
    enhancedHipaaResults;

  // Use checksBreakdown or checks as fallback
  const checksList = checksBreakdown || checks || [];

  const getScoreColor = (score: string | number) => {
    const numScore = typeof score === 'string' ? parseFloat(score) : score;
    if (numScore >= 80) return 'text-green-600';
    if (numScore >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="h-5 w-5 text-green-600" />
    ) : (
      <XCircle className="h-5 w-5 text-red-600" />
    );
  };

  return (
    <div className="space-y-6">
      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            HIPAA Compliance Analysis Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`text-3xl font-bold ${getScoreColor(overallScore)}`}>
                {overallScore}%
              </div>
              <div className="text-sm text-muted-foreground">Overall Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-green-600">{summary.passedChecks}</div>
              <div className="text-sm text-muted-foreground">Passed Checks</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-semibold text-red-600">{summary.failedChecks}</div>
              <div className="text-sm text-muted-foreground">Failed Checks</div>
            </div>
          </div>

          <div className="mt-4">
            <Progress value={(summary.passedChecks / summary.totalChecks) * 100} className="h-3" />
            <div className="text-sm text-muted-foreground mt-1">
              {summary.passedChecks} of {summary.totalChecks} checks passed
            </div>
          </div>

          <div className="mt-4">
            <Badge variant={summary.complianceLevel === 'compliant' ? 'default' : 'destructive'}>
              {summary.complianceLevel}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Checks Breakdown */}
      {checksList.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Check Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {checksList.map((check, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  {getStatusIcon(check.passed)}
                  <div className="flex-1">
                    <div className="font-medium">
                      {String(check.name || check.title || `Check ${index + 1}`)}
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {String(check.description || '')}
                    </div>
                    {check.severity ? (
                      <Badge variant="outline" className="mt-2">
                        {String(check.severity)}
                      </Badge>
                    ) : null}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations && recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recommendations.map((rec, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="font-medium">{rec.title}</div>
                  <div className="text-sm text-muted-foreground mt-1">{rec.description}</div>
                  {rec.priority && (
                    <Badge variant="outline" className="mt-2">
                      Priority: {rec.priority}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metadata */}
      {metadata && (
        <Card>
          <CardHeader>
            <CardTitle>Analysis Metadata</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="font-medium">Processing Time</div>
                <div className="text-muted-foreground">{metadata.processingTime}ms</div>
              </div>
              <div>
                <div className="font-medium">Version</div>
                <div className="text-muted-foreground">{metadata.version}</div>
              </div>
              <div>
                <div className="font-medium">Analysis Levels</div>
                <div className="text-muted-foreground">
                  {metadata.analysisLevelsUsed?.join(', ') || 'N/A'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
