import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, GdprRuleId, GdprCategory, Severity, Recommendation } from '../types';
import { EnhancedBrowserManager, BrowserNavigationOptions, PageAnalysisResult } from './enhanced-browser-manager';
import { SmartUrlResolver, UrlResolutionOptions, ContentDiscoveryResult } from './smart-url-resolver';

export interface EnhancedCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId?: string;
  userAgent?: string;
  retryAttempts?: number;
  enableJavaScript?: boolean;
  enableImages?: boolean;
  followRedirects?: boolean;
}

export interface CheckExecutionContext {
  config: EnhancedCheckConfig;
  urlResolver: SmartUrlResolver;
  browserManager: EnhancedBrowserManager;
  page?: Page;
  browser?: Browser;
  pageAnalysis?: PageAnalysisResult;
  contentDiscovery?: ContentDiscoveryResult;
}

export interface CheckResult {
  passed: boolean;
  score: number;
  evidence: Evidence[];
  recommendations: Recommendation[];
  executionTime: number;
  error?: string;
}

/**
 * Enhanced Check Template for GDPR compliance checks
 * Provides a standardized framework for implementing robust checks
 */
export class EnhancedCheckTemplate {
  private urlResolver: SmartUrlResolver;
  private browserManager: EnhancedBrowserManager;

  constructor() {
    this.urlResolver = new SmartUrlResolver();
    this.browserManager = new EnhancedBrowserManager();
  }

  /**
   * Execute a check with comprehensive error handling and fallbacks
   */
  async executeCheck(
    ruleId: GdprRuleId,
    ruleName: string,
    category: GdprCategory,
    weight: number,
    severity: Severity,
    config: EnhancedCheckConfig,
    checkFunction: (context: CheckExecutionContext) => Promise<CheckResult>,
    requiresBrowser: boolean = true,
    manualReviewRequired: boolean = false
  ): Promise<GdprCheckResult> {
    const startTime = Date.now();
    let context: CheckExecutionContext | null = null;

    try {
      // Create execution context
      context = await this.createExecutionContext(config, requiresBrowser);
      
      // Execute the check function
      const result = await checkFunction(context);
      
      return {
        ruleId,
        ruleName,
        category,
        passed: result.passed,
        score: result.score,
        weight,
        severity,
        evidence: result.evidence,
        recommendations: result.recommendations,
        manualReviewRequired,
      };
    } catch (error) {
      console.error(`Check ${ruleId} failed:`, error);
      
      return this.createFailedResult(
        ruleId,
        ruleName,
        category,
        weight,
        severity,
        error instanceof Error ? error.message : 'Unknown error',
        manualReviewRequired
      );
    } finally {
      // Cleanup resources
      if (context) {
        await this.cleanupContext(context);
      }
    }
  }

  /**
   * Create execution context with URL resolution and browser setup
   */
  private async createExecutionContext(
    config: EnhancedCheckConfig,
    requiresBrowser: boolean
  ): Promise<CheckExecutionContext> {
    const context: CheckExecutionContext = {
      config,
      urlResolver: this.urlResolver,
      browserManager: this.browserManager,
    };

    try {
      // Resolve URL first
      const resolvedUrl = await this.urlResolver.resolveUrl(config.targetUrl, {
        timeout: config.timeout,
        retryAttempts: config.retryAttempts || 3,
        followRedirects: config.followRedirects !== false,
      });

      if (!resolvedUrl.isAccessible) {
        throw new Error(`URL not accessible: ${resolvedUrl.error || 'Unknown error'}`);
      }

      // Update config with resolved URL
      context.config = {
        ...config,
        targetUrl: resolvedUrl.finalUrl,
      };

      // Discover content structure
      context.contentDiscovery = await this.urlResolver.discoverContent(resolvedUrl.finalUrl);

      // Setup browser if required
      if (requiresBrowser) {
        const navigationOptions: BrowserNavigationOptions = {
          timeout: config.timeout,
          userAgent: config.userAgent,
          retryAttempts: config.retryAttempts || 3,
          enableJavaScript: config.enableJavaScript !== false,
          enableImages: config.enableImages !== false,
          blockResources: ['font', 'media'], // Block non-essential resources
        };

        const { page, browser } = await this.browserManager.navigateToUrl(
          resolvedUrl.finalUrl,
          navigationOptions
        );

        context.page = page;
        context.browser = browser;

        // Perform page analysis
        context.pageAnalysis = await this.browserManager.analyzePage(page);
      }

      return context;
    } catch (error) {
      // Cleanup on failure
      await this.cleanupContext(context);
      throw error;
    }
  }

  /**
   * Cleanup execution context resources
   */
  private async cleanupContext(context: CheckExecutionContext): Promise<void> {
    try {
      if (context.page) {
        await context.page.close().catch(() => {});
      }
      
      if (context.browser) {
        await this.browserManager.releaseBrowser(context.browser);
      }
    } catch (error) {
      console.warn('Context cleanup failed:', error);
    }
  }

  /**
   * Create a failed check result
   */
  private createFailedResult(
    ruleId: GdprRuleId,
    ruleName: string,
    category: GdprCategory,
    weight: number,
    severity: Severity,
    errorMessage: string,
    manualReviewRequired: boolean
  ): GdprCheckResult {
    return {
      ruleId,
      ruleName,
      category,
      passed: false,
      score: 0,
      weight,
      severity,
      evidence: [
        {
          type: 'text',
          description: 'Check execution failed',
          value: errorMessage,
        },
      ],
      recommendations: [
        {
          priority: 1,
          title: 'Fix website accessibility',
          description: 'Ensure website is accessible for compliance scanning',
          implementation: 'Check website loading, SSL certificates, and accessibility issues',
          effort: 'moderate',
          impact: 'medium',
        },
      ],
      manualReviewRequired,
    };
  }

  /**
   * Helper: Find privacy policy links with enhanced accuracy
   */
  static findPrivacyPolicyLinks(context: CheckExecutionContext): Array<{ url: string; confidence: number }> {
    const links: Array<{ url: string; confidence: number }> = [];
    
    // Use content discovery results
    if (context.contentDiscovery) {
      for (const link of context.contentDiscovery.privacyPolicyLinks) {
        links.push({
          url: link.url,
          confidence: link.confidence,
        });
      }
    }

    // Fallback to page analysis if available
    if (context.pageAnalysis && links.length === 0) {
      const privacyPatterns = [
        /privacy\s*policy/i,
        /privacy\s*notice/i,
        /datenschutz/i,
        /politique\s*de\s*confidentialité/i,
      ];

      for (const link of context.pageAnalysis.links) {
        for (const pattern of privacyPatterns) {
          if (pattern.test(link.text)) {
            links.push({
              url: link.href,
              confidence: 0.8,
            });
            break;
          }
        }
      }
    }

    return links.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Helper: Check for cookie consent banner
   */
  static async checkCookieConsentBanner(context: CheckExecutionContext): Promise<{
    found: boolean;
    text: string;
    interactionResult?: any;
  }> {
    if (!context.page) {
      return { found: false, text: '' };
    }

    try {
      // Use content discovery first
      if (context.contentDiscovery && context.contentDiscovery.cookieBannerElements.length > 0) {
        const banner = context.contentDiscovery.cookieBannerElements[0];
        
        // Try to interact with the banner
        const interactionResult = await context.browserManager.interactWithConsentBanner(context.page);
        
        return {
          found: true,
          text: banner.text,
          interactionResult,
        };
      }

      // Fallback to manual detection
      const bannerSelectors = [
        '#onetrust-banner-sdk',
        '.cookie-banner',
        '.consent-banner',
        '[id*="cookie"]',
        '[class*="cookie"]',
      ];

      for (const selector of bannerSelectors) {
        try {
          const element = await context.page.$(selector);
          if (element) {
            const text = await element.evaluate(el => el.textContent || '') || '';
            return { found: true, text };
          }
        } catch (error) {
          continue;
        }
      }

      return { found: false, text: '' };
    } catch (error) {
      console.warn('Cookie consent banner check failed:', error);
      return { found: false, text: '' };
    }
  }

  /**
   * Helper: Analyze text content for patterns
   */
  static analyzeTextContent(
    content: string,
    patterns: Array<{ name: string; regex: RegExp; weight: number }>
  ): { score: number; foundPatterns: string[]; evidence: Evidence[] } {
    const foundPatterns: string[] = [];
    const evidence: Evidence[] = [];
    let totalWeight = 0;
    let foundWeight = 0;

    for (const pattern of patterns) {
      totalWeight += pattern.weight;
      
      if (pattern.regex.test(content)) {
        foundPatterns.push(pattern.name);
        foundWeight += pattern.weight;
        
        // Extract evidence text
        const match = content.match(pattern.regex);
        if (match) {
          const index = content.indexOf(match[0]);
          const evidenceText = content.substring(
            Math.max(0, index - 50),
            Math.min(content.length, index + match[0].length + 50)
          );
          
          evidence.push({
            type: 'text',
            description: `Found pattern: ${pattern.name}`,
            value: evidenceText.trim(),
          });
        }
      }
    }

    const score = totalWeight > 0 ? Math.round((foundWeight / totalWeight) * 100) : 0;

    return { score, foundPatterns, evidence };
  }

  /**
   * Helper: Check HTTPS and security headers
   */
  static async checkSecurityFeatures(context: CheckExecutionContext): Promise<{
    httpsEnabled: boolean;
    securityHeaders: Record<string, string>;
    tlsVersion: string;
    certificateValid: boolean;
  }> {
    const result = {
      httpsEnabled: false,
      securityHeaders: {} as Record<string, string>,
      tlsVersion: '',
      certificateValid: false,
    };

    try {
      const url = new URL(context.config.targetUrl);
      result.httpsEnabled = url.protocol === 'https:';

      if (context.page) {
        // Get response headers
        const response = await context.page.goto(context.config.targetUrl, { waitUntil: 'domcontentloaded' });
        if (response) {
          const headers = response.headers();
          
          // Check for security headers
          const securityHeaderNames = [
            'strict-transport-security',
            'content-security-policy',
            'x-frame-options',
            'x-content-type-options',
            'referrer-policy',
            'permissions-policy',
          ];

          for (const headerName of securityHeaderNames) {
            if (headers[headerName]) {
              result.securityHeaders[headerName] = headers[headerName];
            }
          }
        }
      }

      // Additional security checks could be added here
      result.certificateValid = result.httpsEnabled; // Simplified for now

    } catch (error) {
      console.warn('Security features check failed:', error);
    }

    return result;
  }

  /**
   * Cleanup all resources
   */
  async cleanup(): Promise<void> {
    await this.browserManager.cleanup();
  }
}
