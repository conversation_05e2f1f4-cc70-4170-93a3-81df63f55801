/**
 * WCAG Scan Progress Component
 * Real-time progress display for running scans
 */

'use client';

import React, { useEffect, useState } from 'react';
import {
  Play,
  Square,
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { ScanProgressInfo, QueueStatusInfo } from '../../types/wcag';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import { Progress } from '../ui/Progress';
import { Button } from '../ui/Button';
import { Alert } from '../ui/Alert';
import wcagApiService from '../../services/wcag-api';

interface WcagScanProgressProps {
  scanId: string;
  onCancel?: () => void;
  onComplete?: () => void;
  refreshInterval?: number;
}

const WcagScanProgress: React.FC<WcagScanProgressProps> = ({
  scanId,
  onCancel,
  onComplete,
  refreshInterval = 2000
}) => {
  const [progress, setProgress] = useState<ScanProgressInfo | null>(null);
  const [queueStatus, setQueueStatus] = useState<QueueStatusInfo | null>(null);
  const [error, setError] = useState<string>('');
  const [isPolling, setIsPolling] = useState(true);
  const [isCancelling, setIsCancelling] = useState(false);

  /**
   * Fetch progress data
   */
  const fetchProgress = async () => {
    try {
      const progressData = await wcagApiService.getScanProgress(scanId);
      
      if (progressData) {
        setProgress(progressData);

        // Check if scan completed
        if (progressData.status === 'completed' || progressData.status === 'failed') {
          setIsPolling(false);
          if (onComplete) {
            onComplete();
          }
        }
      }

      // Fetch queue status if scan is pending
      if (progressData?.status === 'pending') {
        try {
          const queueData = await wcagApiService.getQueueStatus();
          setQueueStatus(queueData);
        } catch (queueError) {
          // Queue status is optional, don't fail the whole component
          console.warn('Failed to fetch queue status:', queueError);
        }
      }
    } catch (err) {
      setError('Failed to fetch scan progress');
      console.error('Progress fetch error:', err);
    }
  };

  /**
   * Handle scan cancellation
   */
  const handleCancel = async () => {
    try {
      setIsCancelling(true);
      await wcagApiService.cancelScan(scanId);
      setIsPolling(false);
      if (onCancel) {
        onCancel();
      }
    } catch (err) {
      setError('Failed to cancel scan');
      console.error('Cancel error:', err);
    } finally {
      setIsCancelling(false);
    }
  };

  /**
   * Format time remaining
   */
  const formatTimeRemaining = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  /**
   * Get status color and icon
   */
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return { 
          color: 'outline' as const, 
          icon: <Clock className="h-4 w-4" />,
          label: 'PENDING'
        };
      case 'running':
        return { 
          color: 'default' as const, 
          icon: <Play className="h-4 w-4" />,
          label: 'RUNNING'
        };
      case 'completed':
        return { 
          color: 'secondary' as const, 
          icon: <CheckCircle className="h-4 w-4" />,
          label: 'COMPLETED'
        };
      case 'failed':
        return { 
          color: 'destructive' as const, 
          icon: <AlertCircle className="h-4 w-4" />,
          label: 'FAILED'
        };
      case 'cancelled':
        return { 
          color: 'outline' as const, 
          icon: <Square className="h-4 w-4" />,
          label: 'CANCELLED'
        };
      default:
        return { 
          color: 'outline' as const, 
          icon: <Loader2 className="h-4 w-4" />,
          label: 'UNKNOWN'
        };
    }
  };

  // Set up polling
  useEffect(() => {
    if (!isPolling) return;

    const interval = setInterval(fetchProgress, refreshInterval);

    // Initial fetch
    fetchProgress();

    return () => clearInterval(interval);
  }, [scanId, refreshInterval, isPolling]);

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <div>
          <h4 className="font-semibold">Error</h4>
          <p className="text-sm">{error}</p>
        </div>
      </Alert>
    );
  }

  if (!progress) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Loading scan progress...</span>
          </div>
          <Progress value={0} className="mt-4" />
        </CardContent>
      </Card>
    );
  }

  const statusInfo = getStatusInfo(progress.status);

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>WCAG Scan Progress</CardTitle>
          <Badge variant={statusInfo.color} className="flex items-center gap-1">
            {statusInfo.icon}
            {statusInfo.label}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span>Progress: {progress.completedChecks}/{progress.totalChecks} checks</span>
            <span className="font-medium">{progress.progress}%</span>
          </div>
          <Progress value={progress.progress} className="h-3" />
        </div>

        {/* Current Status */}
        {progress.status === 'running' && progress.currentCheck && (
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Currently Running:</p>
            <p className="font-medium">{progress.currentCheck}</p>
          </div>
        )}

        {/* Time Remaining */}
        {progress.estimatedTimeRemaining && progress.status === 'running' && (
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>
              Estimated time remaining: {formatTimeRemaining(progress.estimatedTimeRemaining)}
            </span>
          </div>
        )}

        {/* Queue Information */}
        {queueStatus && progress.status === 'pending' && (
          <Alert>
            <Clock className="h-4 w-4" />
            <div>
              <h4 className="font-semibold">Scan Queued</h4>
              <p className="text-sm">
                Position in queue: {queueStatus.position || queueStatus.totalQueued}
              </p>
              <p className="text-sm">
                Estimated wait time: {formatTimeRemaining(queueStatus.estimatedWaitTime)}
              </p>
            </div>
          </Alert>
        )}

        {/* Completed Checks Preview */}
        {progress.completedChecks > 0 && progress.status === 'running' && (
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Recent Progress:</p>
            <div className="space-y-1">
              <div className="flex items-center space-x-2 text-sm">
                <CheckCircle className="h-3 w-3 text-green-600" />
                <span>Color Contrast Analysis - Completed</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <CheckCircle className="h-3 w-3 text-green-600" />
                <span>Focus Visibility Check - Completed</span>
              </div>
              {progress.currentCheck && (
                <div className="flex items-center space-x-2 text-sm">
                  <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                  <span>{progress.currentCheck} - In Progress</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {progress.status === 'running' && (
          <div className="flex justify-end pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              disabled={isCancelling}
              className="flex items-center gap-2"
            >
              {isCancelling ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Cancelling...
                </>
              ) : (
                <>
                  <Square className="h-4 w-4" />
                  Cancel Scan
                </>
              )}
            </Button>
          </div>
        )}

        {/* Completion Messages */}
        {progress.status === 'completed' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <div>
              <h4 className="font-semibold">Scan Completed Successfully!</h4>
              <p className="text-sm">View detailed results below.</p>
            </div>
          </Alert>
        )}

        {progress.status === 'failed' && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <div>
              <h4 className="font-semibold">Scan Failed</h4>
              <p className="text-sm">
                The WCAG compliance scan could not be completed. Please try again or contact support.
              </p>
            </div>
          </Alert>
        )}

        {progress.status === 'cancelled' && (
          <Alert variant="destructive">
            <Square className="h-4 w-4" />
            <div>
              <h4 className="font-semibold">Scan Cancelled</h4>
              <p className="text-sm">The scan was cancelled by user request.</p>
            </div>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default WcagScanProgress;
