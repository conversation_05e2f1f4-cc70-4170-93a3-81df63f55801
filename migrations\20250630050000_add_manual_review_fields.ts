import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Add manual review fields to gdpr_check_results table
  await knex.schema.alterTable('gdpr_check_results', (table) => {
    table.boolean('manual_review_completed').defaultTo(false);
    table.string('manual_review_assessment', 50).nullable(); // compliant, non-compliant, partially-compliant, needs-review
    table.text('manual_review_notes').nullable();
    table.string('manual_reviewer_name', 100).nullable();
    table.timestamp('manual_reviewed_at').nullable();
    table.uuid('manual_reviewed_by').nullable();
    
    // Add index for faster queries
    table.index(['scan_id', 'manual_review_completed']);
    table.index(['manual_reviewed_by']);
  });

  console.log('✅ Added manual review fields to gdpr_check_results table');
}

export async function down(knex: Knex): Promise<void> {
  // Remove manual review fields from gdpr_check_results table
  await knex.schema.alterTable('gdpr_check_results', (table) => {
    table.dropIndex(['scan_id', 'manual_review_completed']);
    table.dropIndex(['manual_reviewed_by']);
    
    table.dropColumn('manual_review_completed');
    table.dropColumn('manual_review_assessment');
    table.dropColumn('manual_review_notes');
    table.dropColumn('manual_reviewer_name');
    table.dropColumn('manual_reviewed_at');
    table.dropColumn('manual_reviewed_by');
  });

  console.log('✅ Removed manual review fields from gdpr_check_results table');
}
