'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { hipaaDashboardService } from '@/services/hipaa-dashboard-api';
import { ComplianceMetrics, createComplianceMetrics } from '@/components/dashboard/shared';
import { RiskLevelIndicator } from '@/components/dashboard/shared';
import { ScanStatusBadge } from '@/components/dashboard/shared';
import { Shield, FileText, Lock, TestTube, CheckCircle, AlertTriangle } from 'lucide-react';

/**
 * HIPAA Dashboard Integration Test Page
 * Tests all dashboard components and API integration
 */
interface TestResult {
  test: string;
  status: 'success' | 'error';
  message: string;
  data?: unknown;
}

export default function HipaaDashboardTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    setTestResults([]);
    const results: TestResult[] = [];

    // Test 1: Dashboard API Service
    try {
      const dashboardData = await hipaaDashboardService.getDashboardData();
      results.push({
        test: 'Dashboard API Service',
        status: 'success',
        data: dashboardData,
        message: 'Successfully fetched dashboard data',
      });
    } catch (error) {
      results.push({
        test: 'Dashboard API Service',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to fetch dashboard data',
      });
    }

    // Test 2: Privacy Scans API
    try {
      const privacyScans = await hipaaDashboardService.getPrivacyScans(5);
      results.push({
        test: 'Privacy Scans API',
        status: 'success',
        data: privacyScans,
        message: `Fetched ${privacyScans.length} privacy scans`,
      });
    } catch (error) {
      results.push({
        test: 'Privacy Scans API',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to fetch privacy scans',
      });
    }

    // Test 3: Security Scans API
    try {
      const securityScans = await hipaaDashboardService.getSecurityScans(5);
      results.push({
        test: 'Security Scans API',
        status: 'success',
        data: securityScans,
        message: `Fetched ${securityScans.length} security scans`,
      });
    } catch (error) {
      results.push({
        test: 'Security Scans API',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to fetch security scans',
      });
    }

    // Test 4: Dashboard Metrics
    try {
      const metrics = await hipaaDashboardService.getDashboardMetrics();
      results.push({
        test: 'Dashboard Metrics',
        status: 'success',
        data: metrics,
        message: 'Successfully calculated dashboard metrics',
      });
    } catch (error) {
      results.push({
        test: 'Dashboard Metrics',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to calculate dashboard metrics',
      });
    }

    // Test 5: Score Calculation
    try {
      const privacyScans = await hipaaDashboardService.getPrivacyScans(2);
      const securityScans = await hipaaDashboardService.getSecurityScans(2);
      const overallScore = hipaaDashboardService.calculateOverallScore(privacyScans, securityScans);

      results.push({
        test: 'Score Calculation',
        status: 'success',
        data: {
          privacyScans: privacyScans.length,
          securityScans: securityScans.length,
          overallScore,
        },
        message: `Calculated overall score: ${overallScore}%`,
      });
    } catch (error) {
      results.push({
        test: 'Score Calculation',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to calculate overall score',
      });
    }

    // Test 6: Backend API Endpoints
    try {
      const backendResponse = await fetch('/api/v1/compliance/hipaa/dashboard');
      const backendData = await backendResponse.json();

      results.push({
        test: 'Backend API Endpoints',
        status: backendResponse.ok ? 'success' : 'error',
        data: backendData,
        message: backendResponse.ok
          ? 'Backend dashboard endpoint is working'
          : `Backend API error: ${backendResponse.statusText}`,
      });
    } catch (error) {
      results.push({
        test: 'Backend API Endpoints',
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to connect to backend API',
      });
    }

    setTestResults(results);
    setLoading(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <TestTube className="h-8 w-8 text-blue-600" />
        <div>
          <h1 className="text-3xl font-bold text-gray-900">HIPAA Dashboard Integration Test</h1>
          <p className="text-gray-600">Test all dashboard components and API integration</p>
        </div>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={runTests} disabled={loading}>
            {loading ? 'Running Tests...' : 'Run Integration Tests'}
          </Button>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div
                      className={`h-6 w-6 rounded-full flex items-center justify-center ${
                        result.status === 'success'
                          ? 'bg-green-100 text-green-600'
                          : 'bg-red-100 text-red-600'
                      }`}
                    >
                      {result.status === 'success' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <AlertTriangle className="h-4 w-4" />
                      )}
                    </div>
                    <h3 className="font-semibold">{result.test}</h3>
                    <Badge variant={result.status === 'success' ? 'success' : 'destructive'}>
                      {result.status.toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{result.message}</p>
                  {result.error && (
                    <div className="bg-red-50 border border-red-200 rounded p-2 text-sm text-red-800">
                      Error: {result.error}
                    </div>
                  )}
                  {result.data && (
                    <details className="mt-2">
                      <summary className="text-sm font-medium cursor-pointer">View Data</summary>
                      <pre className="mt-2 text-xs bg-gray-50 p-2 rounded overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Component Tests */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Compliance Metrics Test */}
        <Card>
          <CardHeader>
            <CardTitle>Compliance Metrics Component</CardTitle>
          </CardHeader>
          <CardContent>
            <ComplianceMetrics
              title="Sample HIPAA Metrics"
              metrics={createComplianceMetrics.hipaaOverview(82, 88)}
              showProgress={true}
              showTrends={false}
            />
          </CardContent>
        </Card>

        {/* Risk Level Indicators Test */}
        <Card>
          <CardHeader>
            <CardTitle>Risk Level Indicators</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <RiskLevelIndicator riskLevel="low" score={92} variant="card" showProgress={true} />
              <RiskLevelIndicator riskLevel="medium" score={75} variant="inline" />
              <RiskLevelIndicator riskLevel="high" score={55} variant="badge" />
              <RiskLevelIndicator
                riskLevel="critical"
                score={35}
                variant="card"
                showDescription={true}
              />
            </div>
          </CardContent>
        </Card>

        {/* Scan Status Badges Test */}
        <Card>
          <CardHeader>
            <CardTitle>Scan Status Badges</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <ScanStatusBadge status="completed" />
              <ScanStatusBadge status="running" animated={true} />
              <ScanStatusBadge status="failed" />
              <ScanStatusBadge status="pending" />
              <ScanStatusBadge status="cancelled" />
              <ScanStatusBadge status="retrying" animated={true} />
            </div>
          </CardContent>
        </Card>

        {/* Navigation Test */}
        <Card>
          <CardHeader>
            <CardTitle>Navigation Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button variant="outline" size="sm" asChild className="w-full justify-start">
                <a href="/dashboard/hipaa">
                  <Shield className="h-4 w-4 mr-2" />
                  Main HIPAA Dashboard
                </a>
              </Button>
              <Button variant="outline" size="sm" asChild className="w-full justify-start">
                <a href="/dashboard/hipaa/privacy">
                  <FileText className="h-4 w-4 mr-2" />
                  Privacy Scans
                </a>
              </Button>
              <Button variant="outline" size="sm" asChild className="w-full justify-start">
                <a href="/dashboard/hipaa/security">
                  <Lock className="h-4 w-4 mr-2" />
                  Security Scans
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
