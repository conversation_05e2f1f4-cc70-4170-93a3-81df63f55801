# WCAG Implementation Part 02: Core Services & Utilities

## Overview

This document implements the core services and utility functions for WCAG compliance scanning. These utilities provide the foundation for automated accessibility testing with 87% average automation across all rules.

## ⚠️ CRITICAL REQUIREMENTS

### 🚫 NO ANY[] TYPES
**STRICTLY PROHIBITED**: All functions must use strict TypeScript typing. No `any[]` types allowed anywhere.

### ✅ DEPENDENCIES
- **Part 01 Complete**: Database schema, types, and constants must be implemented
- **Real Website Testing**: All utilities must work with actual websites
- **Performance Optimized**: Utilities must support parallel processing

## Prerequisites

- Part 01 (Foundation) completed successfully
- All dependencies from Part 01 available
- Puppeteer and color analysis libraries installed

## Step 1: Install Required Dependencies

### 1.1 Backend Dependencies

```bash
cd backend

# Core accessibility testing dependencies
npm install --save color wcag-contrast css-tree natural compromise
npm install --save puppeteer-extra puppeteer-extra-plugin-stealth
npm install --save readability-js flesch-kincaid

# Development dependencies
npm install --save-dev @types/color @types/css-tree @types/natural
```

### 1.2 Verify Installation

```bash
# Verify all packages installed correctly
npm list color wcag-contrast css-tree natural compromise puppeteer-extra
```

## Step 2: Color Analysis Utilities

### 2.1 Create Color Analyzer

Create `backend/src/compliance/wcag/utils/color-analyzer.ts`:

```typescript
/**
 * Color Analysis Utilities
 * Provides 100% automated color contrast analysis
 */

import Color from 'color';
import { ContrastAnalysisResult } from '../types';

export interface ColorInfo {
  hex: string;
  rgb: { r: number; g: number; b: number };
  hsl: { h: number; s: number; l: number };
  luminance: number;
}

export interface ContrastResult {
  ratio: number;
  level: 'AA' | 'AAA' | 'FAIL';
  isLargeText: boolean;
  passes: boolean;
  recommendation?: string;
}

export class ColorAnalyzer {
  /**
   * Calculate contrast ratio between two colors
   */
  static calculateContrastRatio(foreground: string, background: string): number {
    try {
      const fgColor = Color(foreground);
      const bgColor = Color(background);
      
      const fgLuminance = fgColor.luminosity();
      const bgLuminance = bgColor.luminosity();
      
      const lighter = Math.max(fgLuminance, bgLuminance);
      const darker = Math.min(fgLuminance, bgLuminance);
      
      return (lighter + 0.05) / (darker + 0.05);
    } catch (error) {
      console.error('Error calculating contrast ratio:', error);
      return 0;
    }
  }

  /**
   * Analyze color contrast for accessibility compliance
   */
  static analyzeContrast(
    foreground: string,
    background: string,
    isLargeText: boolean = false
  ): ContrastResult {
    const ratio = this.calculateContrastRatio(foreground, background);
    
    // WCAG contrast requirements
    const aaThreshold = isLargeText ? 3.0 : 4.5;
    const aaaThreshold = isLargeText ? 4.5 : 7.0;
    
    let level: 'AA' | 'AAA' | 'FAIL';
    let passes: boolean;
    let recommendation: string | undefined;
    
    if (ratio >= aaaThreshold) {
      level = 'AAA';
      passes = true;
    } else if (ratio >= aaThreshold) {
      level = 'AA';
      passes = true;
    } else {
      level = 'FAIL';
      passes = false;
      recommendation = this.generateContrastRecommendation(ratio, aaThreshold, isLargeText);
    }
    
    return {
      ratio: Math.round(ratio * 100) / 100,
      level,
      isLargeText,
      passes,
      recommendation
    };
  }

  /**
   * Extract color information from CSS color value
   */
  static extractColorInfo(colorValue: string): ColorInfo {
    try {
      const color = Color(colorValue);
      
      return {
        hex: color.hex(),
        rgb: color.rgb().object(),
        hsl: color.hsl().object(),
        luminance: color.luminosity()
      };
    } catch (error) {
      console.error('Error extracting color info:', error);
      // Return default black color
      return {
        hex: '#000000',
        rgb: { r: 0, g: 0, b: 0 },
        hsl: { h: 0, s: 0, l: 0 },
        luminance: 0
      };
    }
  }

  /**
   * Determine if text is considered large (18pt+ or 14pt+ bold)
   */
  static isLargeText(fontSize: string, fontWeight: string): boolean {
    const size = parseFloat(fontSize);
    const weight = parseInt(fontWeight) || 400;
    
    // Convert various units to pixels (approximate)
    let sizeInPx = size;
    if (fontSize.includes('pt')) {
      sizeInPx = size * 1.33; // pt to px conversion
    } else if (fontSize.includes('em')) {
      sizeInPx = size * 16; // em to px (assuming 16px base)
    } else if (fontSize.includes('rem')) {
      sizeInPx = size * 16; // rem to px (assuming 16px base)
    }
    
    // Large text criteria: 18pt+ (24px+) or 14pt+ (18.7px+) bold
    return sizeInPx >= 24 || (sizeInPx >= 18.7 && weight >= 700);
  }

  /**
   * Generate contrast improvement recommendation
   */
  private static generateContrastRecommendation(
    currentRatio: number,
    targetRatio: number,
    isLargeText: boolean
  ): string {
    const improvement = targetRatio / currentRatio;
    const textType = isLargeText ? 'large text' : 'normal text';
    
    if (improvement <= 1.2) {
      return `Slightly adjust colors to improve contrast for ${textType}. Current ratio: ${currentRatio.toFixed(2)}, target: ${targetRatio}`;
    } else if (improvement <= 2.0) {
      return `Moderate color adjustment needed for ${textType}. Consider darkening text or lightening background.`;
    } else {
      return `Significant color change required for ${textType}. Consider using high-contrast color combinations.`;
    }
  }

  /**
   * Analyze gradient backgrounds for contrast
   */
  static analyzeGradientContrast(
    foregroundColor: string,
    gradientStops: string[]
  ): ContrastResult[] {
    return gradientStops.map(stopColor => 
      this.analyzeContrast(foregroundColor, stopColor)
    );
  }

  /**
   * Find optimal color for contrast improvement
   */
  static suggestOptimalColor(
    baseColor: string,
    targetContrast: number,
    adjustBackground: boolean = true
  ): string {
    try {
      const base = Color(baseColor);
      const baseLuminance = base.luminosity();
      
      // Calculate required luminance for target contrast
      let targetLuminance: number;
      
      if (adjustBackground) {
        // Adjust background to meet contrast with foreground
        if (baseLuminance > 0.5) {
          // Dark background needed
          targetLuminance = (baseLuminance + 0.05) / targetContrast - 0.05;
        } else {
          // Light background needed
          targetLuminance = (baseLuminance + 0.05) * targetContrast - 0.05;
        }
      } else {
        // Adjust foreground to meet contrast with background
        if (baseLuminance > 0.5) {
          // Dark foreground needed
          targetLuminance = (baseLuminance + 0.05) / targetContrast - 0.05;
        } else {
          // Light foreground needed
          targetLuminance = (baseLuminance + 0.05) * targetContrast - 0.05;
        }
      }
      
      // Clamp luminance to valid range
      targetLuminance = Math.max(0, Math.min(1, targetLuminance));
      
      // Convert luminance back to color
      const lightness = Math.sqrt(targetLuminance) * 100;
      return base.lightness(lightness).hex();
      
    } catch (error) {
      console.error('Error suggesting optimal color:', error);
      return adjustBackground ? '#ffffff' : '#000000';
    }
  }
}

/**
 * Color contrast validation patterns
 */
export const COLOR_PATTERNS = {
  // CSS color value patterns
  HEX: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  RGB: /^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/,
  RGBA: /^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)$/,
  HSL: /^hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)$/,
  HSLA: /^hsla\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*([\d.]+)\s*\)$/,
  
  // Named colors (subset of CSS named colors)
  NAMED_COLORS: [
    'black', 'white', 'red', 'green', 'blue', 'yellow', 'cyan', 'magenta',
    'gray', 'grey', 'darkgray', 'darkgrey', 'lightgray', 'lightgrey',
    'transparent'
  ]
};

/**
 * WCAG contrast thresholds
 */
export const CONTRAST_THRESHOLDS = {
  AA_NORMAL: 4.5,
  AA_LARGE: 3.0,
  AAA_NORMAL: 7.0,
  AAA_LARGE: 4.5
} as const;
```

## Step 4: Keyboard Testing Utilities

### 4.1 Create Keyboard Tester

Create `backend/src/compliance/wcag/utils/keyboard-tester.ts`:

```typescript
/**
 * Keyboard Testing Utilities
 * Provides comprehensive keyboard accessibility testing
 */

import { Page } from 'puppeteer';
import { KeyboardAnalysisResult } from '../types';

export interface KeyboardTestResult {
  element: string;
  isReachable: boolean;
  isOperable: boolean;
  hasKeyboardTrap: boolean;
  supportedKeys: string[];
  issues: string[];
  recommendations: string[];
}

export interface KeyboardNavigation {
  totalElements: number;
  reachableElements: number;
  operableElements: number;
  keyboardTraps: number;
  overallScore: number;
  results: KeyboardTestResult[];
}

export class KeyboardTester {
  /**
   * Test keyboard accessibility for all interactive elements
   */
  static async testKeyboardAccessibility(page: Page): Promise<KeyboardNavigation> {
    const interactiveElements = await this.getInteractiveElements(page);
    const results: KeyboardTestResult[] = [];

    let reachableCount = 0;
    let operableCount = 0;
    let trapCount = 0;

    for (const element of interactiveElements) {
      const testResult = await this.testElementKeyboardAccess(page, element);
      results.push(testResult);

      if (testResult.isReachable) reachableCount++;
      if (testResult.isOperable) operableCount++;
      if (testResult.hasKeyboardTrap) trapCount++;
    }

    const overallScore = interactiveElements.length > 0
      ? Math.round((operableCount / interactiveElements.length) * 100)
      : 100;

    return {
      totalElements: interactiveElements.length,
      reachableElements: reachableCount,
      operableElements: operableCount,
      keyboardTraps: trapCount,
      overallScore,
      results
    };
  }

  /**
   * Get all interactive elements that should be keyboard accessible
   */
  private static async getInteractiveElements(page: Page): Promise<string[]> {
    return await page.evaluate(() => {
      const selectors = [
        'button:not([disabled])',
        'a[href]',
        'input:not([disabled]):not([type="hidden"])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        '[tabindex]:not([tabindex="-1"])',
        '[role="button"]:not([disabled])',
        '[role="link"]',
        '[role="menuitem"]',
        '[role="tab"]',
        '[role="checkbox"]',
        '[role="radio"]',
        '[contenteditable="true"]'
      ];

      const elements: string[] = [];

      selectors.forEach(selector => {
        const nodeList = document.querySelectorAll(selector);
        nodeList.forEach((element) => {
          const htmlElement = element as HTMLElement;
          const computedStyle = window.getComputedStyle(htmlElement);

          // Check if element is visible
          const isVisible = computedStyle.display !== 'none' &&
                           computedStyle.visibility !== 'hidden' &&
                           computedStyle.opacity !== '0';

          if (isVisible) {
            elements.push(this.generateUniqueSelector(htmlElement));
          }
        });
      });

      return elements;
    });
  }

  /**
   * Test keyboard accessibility for a specific element
   */
  private static async testElementKeyboardAccess(
    page: Page,
    selector: string
  ): Promise<KeyboardTestResult> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    const supportedKeys: string[] = [];

    try {
      // Test if element is reachable via Tab
      const isReachable = await this.testTabReachability(page, selector);
      if (!isReachable) {
        issues.push('Element not reachable via Tab navigation');
        recommendations.push('Ensure element has proper tabindex or is naturally focusable');
      }

      // Test if element is operable via keyboard
      const isOperable = await this.testKeyboardOperation(page, selector, supportedKeys);
      if (!isOperable) {
        issues.push('Element not operable via keyboard');
        recommendations.push('Add keyboard event handlers for Enter/Space keys');
      }

      // Test for keyboard traps
      const hasKeyboardTrap = await this.testKeyboardTrap(page, selector);
      if (hasKeyboardTrap) {
        issues.push('Keyboard trap detected - focus cannot escape');
        recommendations.push('Ensure Escape key or other mechanism allows focus to leave');
      }

      return {
        element: selector,
        isReachable,
        isOperable,
        hasKeyboardTrap,
        supportedKeys,
        issues,
        recommendations
      };

    } catch (error) {
      console.error(`Error testing keyboard access for ${selector}:`, error);
      return {
        element: selector,
        isReachable: false,
        isOperable: false,
        hasKeyboardTrap: false,
        supportedKeys: [],
        issues: ['Error during keyboard testing'],
        recommendations: ['Manual testing required']
      };
    }
  }

  /**
   * Test if element is reachable via Tab navigation
   */
  private static async testTabReachability(page: Page, selector: string): Promise<boolean> {
    try {
      // Start from body and tab through elements
      await page.focus('body');

      // Tab through up to 50 elements to find the target
      for (let i = 0; i < 50; i++) {
        await page.keyboard.press('Tab');

        const focusedElement = await page.evaluate(() => {
          const focused = document.activeElement;
          return focused ? this.generateUniqueSelector(focused as HTMLElement) : null;
        });

        if (focusedElement === selector) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('Error testing tab reachability:', error);
      return false;
    }
  }

  /**
   * Test if element responds to keyboard operations
   */
  private static async testKeyboardOperation(
    page: Page,
    selector: string,
    supportedKeys: string[]
  ): Promise<boolean> {
    try {
      await page.focus(selector);

      // Get element type to determine appropriate keys
      const elementInfo = await page.evaluate((sel) => {
        const element = document.querySelector(sel) as HTMLElement;
        if (!element) return null;

        return {
          tagName: element.tagName.toLowerCase(),
          type: element.getAttribute('type'),
          role: element.getAttribute('role')
        };
      }, selector);

      if (!elementInfo) return false;

      let isOperable = false;

      // Test appropriate keys based on element type
      const keysToTest = this.getKeysForElement(elementInfo);

      for (const key of keysToTest) {
        const responded = await this.testKeyResponse(page, selector, key);
        if (responded) {
          supportedKeys.push(key);
          isOperable = true;
        }
      }

      return isOperable;
    } catch (error) {
      console.error('Error testing keyboard operation:', error);
      return false;
    }
  }

  /**
   * Test for keyboard traps
   */
  private static async testKeyboardTrap(page: Page, selector: string): Promise<boolean> {
    try {
      await page.focus(selector);

      // Try to escape using common methods
      const escapeKeys = ['Escape', 'Tab', 'Shift+Tab'];

      for (const key of escapeKeys) {
        await page.keyboard.press(key);

        const focusedElement = await page.evaluate(() => {
          const focused = document.activeElement;
          return focused ? this.generateUniqueSelector(focused as HTMLElement) : null;
        });

        // If focus moved away, no trap detected
        if (focusedElement !== selector) {
          return false;
        }
      }

      // If focus never moved, potential trap detected
      return true;
    } catch (error) {
      console.error('Error testing keyboard trap:', error);
      return false;
    }
  }

  /**
   * Test if element responds to specific key
   */
  private static async testKeyResponse(
    page: Page,
    selector: string,
    key: string
  ): Promise<boolean> {
    try {
      // Set up event listener to detect response
      const responded = await page.evaluate((sel, keyToTest) => {
        return new Promise<boolean>((resolve) => {
          const element = document.querySelector(sel) as HTMLElement;
          if (!element) {
            resolve(false);
            return;
          }

          let hasResponded = false;
          const timeout = setTimeout(() => resolve(hasResponded), 100);

          // Listen for various events that indicate response
          const events = ['click', 'change', 'input', 'keydown', 'keyup'];
          const listeners: Array<() => void> = [];

          events.forEach(eventType => {
            const listener = () => {
              hasResponded = true;
              clearTimeout(timeout);
              // Clean up listeners
              listeners.forEach(l => l());
              resolve(true);
            };
            element.addEventListener(eventType, listener, { once: true });
            listeners.push(() => element.removeEventListener(eventType, listener));
          });
        });
      }, selector, key);

      // Press the key
      await page.keyboard.press(key);

      return responded;
    } catch (error) {
      console.error('Error testing key response:', error);
      return false;
    }
  }

  /**
   * Get appropriate keys to test for element type
   */
  private static getKeysForElement(elementInfo: {
    tagName: string;
    type: string | null;
    role: string | null;
  }): string[] {
    const { tagName, type, role } = elementInfo;

    // Default keys for most interactive elements
    let keys = ['Enter', 'Space'];

    // Specific keys based on element type
    if (tagName === 'a' || role === 'link') {
      keys = ['Enter']; // Links typically only respond to Enter
    } else if (tagName === 'button' || role === 'button') {
      keys = ['Enter', 'Space']; // Buttons respond to both
    } else if (tagName === 'input') {
      if (type === 'checkbox' || type === 'radio') {
        keys = ['Space']; // Checkboxes and radios use Space
      } else if (type === 'submit' || type === 'button') {
        keys = ['Enter', 'Space'];
      } else {
        keys = ['Enter']; // Text inputs use Enter
      }
    } else if (tagName === 'select') {
      keys = ['Enter', 'Space', 'ArrowDown', 'ArrowUp'];
    } else if (role === 'tab') {
      keys = ['Enter', 'Space', 'ArrowLeft', 'ArrowRight'];
    } else if (role === 'menuitem') {
      keys = ['Enter', 'Space'];
    }

    return keys;
  }

  /**
   * Generate unique selector for element
   */
  private static generateUniqueSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }

    // Generate nth-child selector
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element) + 1;
      return `${element.tagName.toLowerCase()}:nth-child(${index})`;
    }

    return element.tagName.toLowerCase();
  }
}

/**
 * Keyboard testing configuration
 */
export const KEYBOARD_CONFIG = {
  // Maximum elements to tab through when testing reachability
  MAX_TAB_ATTEMPTS: 50,

  // Timeout for key response testing
  KEY_RESPONSE_TIMEOUT: 100,

  // Keys that should allow escape from keyboard traps
  ESCAPE_KEYS: ['Escape', 'Tab', 'Shift+Tab'],

  // Minimum score for keyboard accessibility
  MIN_ACCESSIBILITY_SCORE: 80
} as const;
```

## Step 5: Layout Analysis Utilities

### 5.1 Create Layout Analyzer

Create `backend/src/compliance/wcag/utils/layout-analyzer.ts`:

```typescript
/**
 * Layout Analysis Utilities
 * Provides visual layout and positioning analysis
 */

import { Page } from 'puppeteer';

export interface ElementPosition {
  selector: string;
  x: number;
  y: number;
  width: number;
  height: number;
  zIndex: number;
  isVisible: boolean;
}

export interface LayoutAnalysis {
  elements: ElementPosition[];
  overlaps: ElementOverlap[];
  readingOrder: string[];
  visualOrder: string[];
  isLogicalOrder: boolean;
}

export interface ElementOverlap {
  element1: string;
  element2: string;
  overlapArea: number;
  isSignificant: boolean;
}

export class LayoutAnalyzer {
  /**
   * Analyze page layout and element positioning
   */
  static async analyzeLayout(page: Page, selectors: string[]): Promise<LayoutAnalysis> {
    const elements = await this.getElementPositions(page, selectors);
    const overlaps = this.detectOverlaps(elements);
    const readingOrder = this.determineReadingOrder(elements);
    const visualOrder = this.determineVisualOrder(elements);
    const isLogicalOrder = this.compareOrders(readingOrder, visualOrder);

    return {
      elements,
      overlaps,
      readingOrder,
      visualOrder,
      isLogicalOrder
    };
  }

  /**
   * Get positions of all specified elements
   */
  private static async getElementPositions(
    page: Page,
    selectors: string[]
  ): Promise<ElementPosition[]> {
    return await page.evaluate((sels) => {
      const positions: ElementPosition[] = [];

      sels.forEach(selector => {
        const element = document.querySelector(selector) as HTMLElement;
        if (element) {
          const rect = element.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(element);

          positions.push({
            selector,
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height,
            zIndex: parseInt(computedStyle.zIndex) || 0,
            isVisible: computedStyle.display !== 'none' &&
                     computedStyle.visibility !== 'hidden' &&
                     computedStyle.opacity !== '0'
          });
        }
      });

      return positions;
    }, selectors);
  }

  /**
   * Detect overlapping elements
   */
  private static detectOverlaps(elements: ElementPosition[]): ElementOverlap[] {
    const overlaps: ElementOverlap[] = [];

    for (let i = 0; i < elements.length; i++) {
      for (let j = i + 1; j < elements.length; j++) {
        const el1 = elements[i];
        const el2 = elements[j];

        const overlapArea = this.calculateOverlapArea(el1, el2);
        if (overlapArea > 0) {
          const isSignificant = overlapArea > Math.min(
            el1.width * el1.height,
            el2.width * el2.height
          ) * 0.1; // 10% overlap threshold

          overlaps.push({
            element1: el1.selector,
            element2: el2.selector,
            overlapArea,
            isSignificant
          });
        }
      }
    }

    return overlaps;
  }

  /**
   * Calculate overlap area between two elements
   */
  private static calculateOverlapArea(el1: ElementPosition, el2: ElementPosition): number {
    const left = Math.max(el1.x, el2.x);
    const right = Math.min(el1.x + el1.width, el2.x + el2.width);
    const top = Math.max(el1.y, el2.y);
    const bottom = Math.min(el1.y + el1.height, el2.y + el2.height);

    if (left < right && top < bottom) {
      return (right - left) * (bottom - top);
    }

    return 0;
  }

  /**
   * Determine reading order based on DOM structure
   */
  private static determineReadingOrder(elements: ElementPosition[]): string[] {
    // Reading order follows DOM order for elements
    return elements.map(el => el.selector);
  }

  /**
   * Determine visual order based on position
   */
  private static determineVisualOrder(elements: ElementPosition[]): string[] {
    return elements
      .filter(el => el.isVisible)
      .sort((a, b) => {
        // Sort by Y position first (top to bottom)
        const yDiff = a.y - b.y;
        if (Math.abs(yDiff) > 10) { // 10px tolerance
          return yDiff;
        }
        // Then by X position (left to right)
        return a.x - b.x;
      })
      .map(el => el.selector);
  }

  /**
   * Compare reading order with visual order
   */
  private static compareOrders(readingOrder: string[], visualOrder: string[]): boolean {
    if (readingOrder.length !== visualOrder.length) {
      return false;
    }

    // Allow some flexibility in order matching
    let matches = 0;
    const tolerance = Math.ceil(readingOrder.length * 0.8); // 80% match required

    for (let i = 0; i < readingOrder.length; i++) {
      const readingIndex = readingOrder.indexOf(visualOrder[i]);
      if (readingIndex >= 0 && Math.abs(readingIndex - i) <= 2) {
        matches++;
      }
    }

    return matches >= tolerance;
  }

  /**
   * Check if element is obscured by fixed/sticky elements
   */
  static async checkElementObscured(
    page: Page,
    selector: string
  ): Promise<{ isObscured: boolean; obscuringElements: string[] }> {
    return await page.evaluate((sel) => {
      const element = document.querySelector(sel) as HTMLElement;
      if (!element) {
        return { isObscured: true, obscuringElements: [] };
      }

      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      // Get all elements at the center point
      const elementsAtPoint = document.elementsFromPoint(centerX, centerY);
      const obscuringElements: string[] = [];

      // Check if any elements above our target are fixed/sticky
      for (const el of elementsAtPoint) {
        if (el === element) break; // Found our element, stop checking

        const style = window.getComputedStyle(el as HTMLElement);
        if (style.position === 'fixed' || style.position === 'sticky') {
          obscuringElements.push(this.generateSelector(el as HTMLElement));
        }
      }

      return {
        isObscured: obscuringElements.length > 0,
        obscuringElements
      };
    }, selector);
  }

  /**
   * Measure target size for touch accessibility
   */
  static async measureTargetSize(page: Page, selector: string): Promise<{
    width: number;
    height: number;
    meetsMinimum: boolean;
    hasAdequateSpacing: boolean;
    nearbyTargets: string[];
  }> {
    return await page.evaluate((sel) => {
      const element = document.querySelector(sel) as HTMLElement;
      if (!element) {
        return {
          width: 0,
          height: 0,
          meetsMinimum: false,
          hasAdequateSpacing: false,
          nearbyTargets: []
        };
      }

      const rect = element.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;

      // WCAG 2.2 minimum target size: 24x24 CSS pixels
      const meetsMinimum = width >= 24 && height >= 24;

      // Check spacing to nearby targets
      const allInteractive = document.querySelectorAll(
        'button, a[href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      const nearbyTargets: string[] = [];
      let hasAdequateSpacing = true;

      Array.from(allInteractive).forEach(other => {
        if (other === element) return;

        const otherRect = (other as HTMLElement).getBoundingClientRect();
        const distance = Math.sqrt(
          Math.pow(rect.left - otherRect.left, 2) +
          Math.pow(rect.top - otherRect.top, 2)
        );

        // If targets are closer than 16px and don't meet minimum size
        if (distance < 16 && (!meetsMinimum || otherRect.width < 24 || otherRect.height < 24)) {
          hasAdequateSpacing = false;
          nearbyTargets.push(this.generateSelector(other as HTMLElement));
        }
      });

      return {
        width,
        height,
        meetsMinimum,
        hasAdequateSpacing,
        nearbyTargets
      };
    }, selector);
  }

  /**
   * Generate CSS selector for element
   */
  private static generateSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }

    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element) + 1;
      return `${element.tagName.toLowerCase()}:nth-child(${index})`;
    }

    return element.tagName.toLowerCase();
  }
}

/**
 * Layout analysis configuration
 */
export const LAYOUT_CONFIG = {
  // Minimum target size (WCAG 2.2)
  MIN_TARGET_SIZE: 24,

  // Minimum spacing between targets
  MIN_TARGET_SPACING: 16,

  // Position tolerance for order comparison
  POSITION_TOLERANCE: 10,

  // Overlap significance threshold
  OVERLAP_THRESHOLD: 0.1, // 10%

  // Order matching tolerance
  ORDER_MATCH_TOLERANCE: 0.8 // 80%
} as const;
```

## Step 6: Orchestrator Foundation

### 6.1 Create Basic Orchestrator

Create `backend/src/compliance/wcag/orchestrator.ts`:

```typescript
/**
 * WCAG Orchestrator Foundation
 * Coordinates all WCAG compliance checks
 */

import { WcagScanConfig, WcagScanResult, WcagScanSummary } from './types';
import { WCAG_RULES } from './constants';

export class WcagOrchestrator {
  /**
   * Perform comprehensive WCAG scan
   * This is the foundation - individual checks will be implemented in subsequent parts
   */
  async performComprehensiveScan(
    userId: string,
    config: WcagScanConfig
  ): Promise<WcagScanResult> {
    // Foundation implementation - will be expanded in subsequent parts
    console.log(`Starting WCAG scan for ${config.targetUrl}`);

    // This will be implemented in Parts 03-05
    throw new Error('WCAG checks not yet implemented - continue with Parts 03-05');
  }

  /**
   * Calculate scan summary
   */
  private calculateSummary(): WcagScanSummary {
    // Foundation implementation
    return {
      totalChecks: WCAG_RULES.length,
      passedChecks: 0,
      failedChecks: 0,
      manualReviewRequired: 0,
      categoryScores: {
        perceivable: 0,
        operable: 0,
        understandable: 0,
        robust: 0
      },
      versionScores: {
        wcag21: 0,
        wcag22: 0,
        wcag30: 0
      },
      automationRate: 0.87
    };
  }
}
```

## Validation Checklist

- [ ] All dependencies installed successfully
- [ ] ColorAnalyzer provides 100% automated contrast analysis
- [ ] FocusTracker provides comprehensive focus testing
- [ ] KeyboardTester provides automated keyboard accessibility testing
- [ ] LayoutAnalyzer provides positioning and overlap detection
- [ ] All utilities use strict TypeScript (no `any[]` types)
- [ ] Orchestrator foundation ready for check implementations
- [ ] Ready for Part 03 implementation

## Next Steps

Continue with **Part 03: Fully Automated Checks** to implement the 6 rules that can be 100% automated without any manual review.

---

*These core utilities provide the foundation for achieving 87% automation across all WCAG rules with strict TypeScript compliance and real website testing capabilities.*

## Step 3: Focus Tracking Utilities

### 3.1 Create Focus Tracker

Create `backend/src/compliance/wcag/utils/focus-tracker.ts`:

```typescript
/**
 * Focus Tracking Utilities
 * Provides comprehensive focus management analysis
 */

import { Page } from 'puppeteer';
import { FocusAnalysisResult } from '../types';
import { ColorAnalyzer } from './color-analyzer';

export interface FocusableElement {
  selector: string;
  tagName: string;
  role?: string;
  tabIndex: number;
  isVisible: boolean;
  hasVisibleFocus: boolean;
  focusIndicator: FocusIndicator;
  boundingBox: ElementBounds;
}

export interface FocusIndicator {
  hasOutline: boolean;
  outlineWidth: number;
  outlineColor: string;
  contrastRatio: number;
  isVisible: boolean;
  recommendation?: string;
}

export interface ElementBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FocusOrder {
  elements: FocusableElement[];
  isLogical: boolean;
  issues: string[];
  recommendations: string[];
}

export class FocusTracker {
  /**
   * Get all focusable elements on the page
   */
  static async getFocusableElements(page: Page): Promise<FocusableElement[]> {
    return await page.evaluate(() => {
      const focusableSelectors = [
        'a[href]',
        'button',
        'input:not([disabled])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        '[tabindex]:not([tabindex="-1"])',
        '[contenteditable="true"]',
        'audio[controls]',
        'video[controls]',
        'details summary'
      ];
      
      const elements: FocusableElement[] = [];
      
      focusableSelectors.forEach(selector => {
        const nodeList = document.querySelectorAll(selector);
        nodeList.forEach((element, index) => {
          const htmlElement = element as HTMLElement;
          const computedStyle = window.getComputedStyle(htmlElement);
          
          // Check if element is visible
          const isVisible = computedStyle.display !== 'none' &&
                           computedStyle.visibility !== 'hidden' &&
                           computedStyle.opacity !== '0';
          
          if (isVisible) {
            const rect = htmlElement.getBoundingClientRect();
            
            elements.push({
              selector: this.generateSelector(htmlElement),
              tagName: htmlElement.tagName.toLowerCase(),
              role: htmlElement.getAttribute('role') || undefined,
              tabIndex: htmlElement.tabIndex,
              isVisible,
              hasVisibleFocus: false, // Will be determined by focus testing
              focusIndicator: {
                hasOutline: false,
                outlineWidth: 0,
                outlineColor: '',
                contrastRatio: 0,
                isVisible: false
              },
              boundingBox: {
                x: rect.x,
                y: rect.y,
                width: rect.width,
                height: rect.height
              }
            });
          }
        });
      });
      
      return elements;
    });
  }

  /**
   * Test focus visibility for an element
   */
  static async testFocusVisibility(
    page: Page,
    element: FocusableElement
  ): Promise<FocusIndicator> {
    try {
      // Focus the element
      await page.focus(element.selector);
      
      // Get focus indicator styles
      const focusStyles = await page.evaluate((selector) => {
        const el = document.querySelector(selector) as HTMLElement;
        if (!el) return null;
        
        const computedStyle = window.getComputedStyle(el);
        const focusStyle = window.getComputedStyle(el, ':focus');
        
        return {
          outline: focusStyle.outline,
          outlineWidth: focusStyle.outlineWidth,
          outlineColor: focusStyle.outlineColor,
          outlineStyle: focusStyle.outlineStyle,
          border: focusStyle.border,
          borderWidth: focusStyle.borderWidth,
          borderColor: focusStyle.borderColor,
          backgroundColor: focusStyle.backgroundColor,
          boxShadow: focusStyle.boxShadow
        };
      }, element.selector);
      
      if (!focusStyles) {
        return {
          hasOutline: false,
          outlineWidth: 0,
          outlineColor: '',
          contrastRatio: 0,
          isVisible: false,
          recommendation: 'Element not found or not focusable'
        };
      }
      
      // Analyze focus indicator
      const hasOutline = focusStyles.outlineWidth !== '0px' && 
                        focusStyles.outlineStyle !== 'none';
      const outlineWidth = parseFloat(focusStyles.outlineWidth) || 0;
      const outlineColor = focusStyles.outlineColor || '';
      
      // Calculate contrast ratio if outline exists
      let contrastRatio = 0;
      if (hasOutline && outlineColor) {
        // Get background color of parent or page
        const backgroundColor = await this.getEffectiveBackgroundColor(page, element.selector);
        contrastRatio = ColorAnalyzer.calculateContrastRatio(outlineColor, backgroundColor);
      }
      
      const isVisible = hasOutline && outlineWidth >= 1 && contrastRatio >= 3.0;
      
      return {
        hasOutline,
        outlineWidth,
        outlineColor,
        contrastRatio: Math.round(contrastRatio * 100) / 100,
        isVisible,
        recommendation: isVisible ? undefined : this.generateFocusRecommendation(
          hasOutline,
          outlineWidth,
          contrastRatio
        )
      };
      
    } catch (error) {
      console.error('Error testing focus visibility:', error);
      return {
        hasOutline: false,
        outlineWidth: 0,
        outlineColor: '',
        contrastRatio: 0,
        isVisible: false,
        recommendation: 'Error testing focus visibility'
      };
    }
  }

  /**
   * Analyze focus order for logical sequence
   */
  static async analyzeFocusOrder(page: Page): Promise<FocusOrder> {
    const elements = await this.getFocusableElements(page);
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Sort elements by tab order and visual position
    const tabOrderElements = elements
      .filter(el => el.tabIndex >= 0)
      .sort((a, b) => {
        // First sort by tabIndex (0 comes after positive values)
        if (a.tabIndex > 0 && b.tabIndex > 0) {
          return a.tabIndex - b.tabIndex;
        }
        if (a.tabIndex > 0) return -1;
        if (b.tabIndex > 0) return 1;
        
        // Then sort by visual position (top to bottom, left to right)
        const yDiff = a.boundingBox.y - b.boundingBox.y;
        if (Math.abs(yDiff) > 10) { // Allow 10px tolerance
          return yDiff;
        }
        return a.boundingBox.x - b.boundingBox.x;
      });
    
    // Check for logical order issues
    let isLogical = true;
    
    for (let i = 1; i < tabOrderElements.length; i++) {
      const current = tabOrderElements[i];
      const previous = tabOrderElements[i - 1];
      
      // Check if focus jumps significantly in visual position
      const yJump = Math.abs(current.boundingBox.y - previous.boundingBox.y);
      const xJump = Math.abs(current.boundingBox.x - previous.boundingBox.x);
      
      if (yJump > 100 && xJump > 200) { // Significant visual jump
        isLogical = false;
        issues.push(`Focus jumps from ${previous.selector} to ${current.selector} - significant visual distance`);
      }
      
      // Check for positive tabindex values (generally discouraged)
      if (current.tabIndex > 0) {
        issues.push(`Element ${current.selector} uses positive tabindex (${current.tabIndex}) - consider using 0 or removing`);
        recommendations.push('Use tabindex="0" for focusable elements and rely on DOM order for sequence');
      }
    }
    
    // Check for missing focusable elements
    const interactiveElements = await page.evaluate(() => {
      const interactive = document.querySelectorAll('button, a, input, select, textarea');
      return Array.from(interactive).filter(el => {
        const style = window.getComputedStyle(el as HTMLElement);
        return style.display !== 'none' && style.visibility !== 'hidden';
      }).length;
    });
    
    if (tabOrderElements.length < interactiveElements) {
      issues.push('Some interactive elements may not be keyboard accessible');
      recommendations.push('Ensure all interactive elements are focusable via keyboard');
    }
    
    return {
      elements: tabOrderElements,
      isLogical,
      issues,
      recommendations
    };
  }

  /**
   * Check if focused element is obscured by other content
   */
  static async checkFocusObscured(page: Page, selector: string): Promise<boolean> {
    return await page.evaluate((sel) => {
      const element = document.querySelector(sel) as HTMLElement;
      if (!element) return true;
      
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      // Check if element at center point is the focused element or its child
      const elementAtPoint = document.elementFromPoint(centerX, centerY);
      return !element.contains(elementAtPoint);
    }, selector);
  }

  /**
   * Get effective background color for contrast calculation
   */
  private static async getEffectiveBackgroundColor(page: Page, selector: string): Promise<string> {
    return await page.evaluate((sel) => {
      const element = document.querySelector(sel) as HTMLElement;
      if (!element) return '#ffffff';
      
      let current = element;
      while (current && current !== document.body) {
        const style = window.getComputedStyle(current);
        const bgColor = style.backgroundColor;
        
        if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
          return bgColor;
        }
        current = current.parentElement as HTMLElement;
      }
      
      return '#ffffff'; // Default to white
    }, selector);
  }

  /**
   * Generate focus improvement recommendation
   */
  private static generateFocusRecommendation(
    hasOutline: boolean,
    outlineWidth: number,
    contrastRatio: number
  ): string {
    if (!hasOutline) {
      return 'Add visible focus indicator (outline, border, or background change)';
    }
    
    if (outlineWidth < 2) {
      return 'Increase focus indicator thickness to at least 2px';
    }
    
    if (contrastRatio < 3.0) {
      return `Improve focus indicator contrast ratio (current: ${contrastRatio.toFixed(2)}, required: 3.0)`;
    }
    
    return 'Focus indicator meets basic requirements but could be enhanced';
  }

  /**
   * Generate unique CSS selector for element
   */
  private static generateSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }
    
    // Generate nth-child selector
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element) + 1;
      return `${element.tagName.toLowerCase()}:nth-child(${index})`;
    }
    
    return element.tagName.toLowerCase();
  }
}

/**
 * Focus testing configuration
 */
export const FOCUS_CONFIG = {
  // Minimum contrast ratio for focus indicators
  MIN_CONTRAST_RATIO: 3.0,
  
  // Minimum focus indicator thickness
  MIN_INDICATOR_WIDTH: 2.0,
  
  // Visual jump thresholds for focus order
  MAX_VISUAL_JUMP_Y: 100,
  MAX_VISUAL_JUMP_X: 200,
  
  // Position tolerance for logical order
  POSITION_TOLERANCE: 10
} as const;
```
