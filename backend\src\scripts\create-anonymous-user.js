/**
 * <PERSON><PERSON><PERSON> to create anonymous user for GDPR scans
 * This fixes the foreign key constraint violation for anonymous users
 */

const { Client } = require('pg');
require('dotenv').config({ path: '../../.env' });

const ANONYMOUS_USER_UUID = '00000000-0000-0000-0000-000000000000';

async function createAnonymousUser() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'comply_checker',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
  });

  try {
    await client.connect();
    console.log('🔗 Connected to database');

    // Check if anonymous user already exists
    const existingUser = await client.query('SELECT id FROM users WHERE id = $1', [
      ANONYMOUS_USER_UUID,
    ]);

    if (existingUser.rows.length > 0) {
      console.log('✅ Anonymous user already exists');
      return;
    }

    // Create anonymous user
    console.log('📝 Creating anonymous user...');
    await client.query(
      `
      INSERT INTO users (
        id, 
        email, 
        username, 
        first_name, 
        last_name, 
        is_active, 
        is_verified, 
        role, 
        created_at, 
        updated_at
      ) VALUES (
        $1, 
        '<EMAIL>', 
        'anonymous', 
        'Anonymous', 
        'User', 
        true, 
        false, 
        'user', 
        NOW(), 
        NOW()
      )
    `,
      [ANONYMOUS_USER_UUID],
    );

    console.log('✅ Anonymous user created successfully');
    console.log(`   UUID: ${ANONYMOUS_USER_UUID}`);
    console.log('   This user will be used for all anonymous GDPR scans');
  } catch (error) {
    console.error('❌ Error creating anonymous user:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
createAnonymousUser()
  .then(() => {
    console.log('\n🎉 Anonymous user setup completed successfully!');
    console.log('GDPR scans from anonymous users will now work without foreign key errors.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Failed to create anonymous user:', error);
    process.exit(1);
  });
