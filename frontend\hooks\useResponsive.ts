/**
 * Responsive Design Hooks
 * Provides React hooks for responsive behavior and breakpoint detection
 */

import { useState, useEffect } from 'react';

// Breakpoint definitions
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof breakpoints;

/**
 * Hook to detect current screen size and breakpoints
 */
export function useBreakpoint() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('sm');

  useEffect(() => {
    function handleResize() {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setWindowSize({ width, height });

      // Determine current breakpoint
      if (width >= breakpoints['2xl']) {
        setCurrentBreakpoint('2xl');
      } else if (width >= breakpoints.xl) {
        setCurrentBreakpoint('xl');
      } else if (width >= breakpoints.lg) {
        setCurrentBreakpoint('lg');
      } else if (width >= breakpoints.md) {
        setCurrentBreakpoint('md');
      } else {
        setCurrentBreakpoint('sm');
      }
    }

    // Set initial values
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    windowSize,
    currentBreakpoint,
    isMobile: windowSize.width < breakpoints.md,
    isTablet: windowSize.width >= breakpoints.md && windowSize.width < breakpoints.lg,
    isDesktop: windowSize.width >= breakpoints.lg,
    isSmallScreen: windowSize.width < breakpoints.lg,
    isLargeScreen: windowSize.width >= breakpoints.lg,
  };
}

/**
 * Hook to check if screen is at or above a specific breakpoint
 */
export function useMediaQuery(breakpoint: Breakpoint): boolean {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(`(min-width: ${breakpoints[breakpoint]}px)`);

    setMatches(mediaQuery.matches);

    function handleChange(event: MediaQueryListEvent) {
      setMatches(event.matches);
    }

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [breakpoint]);

  return matches;
}

/**
 * Hook for responsive grid columns
 */
export function useResponsiveGrid(config: { mobile?: number; tablet?: number; desktop?: number }) {
  const { isMobile: _isMobile, isTablet, isDesktop } = useBreakpoint(); // eslint-disable-line @typescript-eslint/no-unused-vars

  if (isDesktop && config.desktop) {
    return config.desktop;
  }
  if (isTablet && config.tablet) {
    return config.tablet;
  }
  return config.mobile || 1;
}

/**
 * Hook for responsive values
 */
export function useResponsiveValue<T>(values: { mobile: T; tablet?: T; desktop?: T }): T {
  const { isMobile: _isMobile, isTablet, isDesktop } = useBreakpoint(); // eslint-disable-line @typescript-eslint/no-unused-vars

  if (isDesktop && values.desktop !== undefined) {
    return values.desktop;
  }
  if (isTablet && values.tablet !== undefined) {
    return values.tablet;
  }
  return values.mobile;
}

/**
 * Hook for responsive spacing
 */
export function useResponsiveSpacing() {
  const { currentBreakpoint } = useBreakpoint();

  const spacing = {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
  };

  switch (currentBreakpoint) {
    case '2xl':
      return {
        container: spacing['2xl'],
        section: spacing.xl,
        card: spacing.lg,
        element: spacing.md,
      };
    case 'xl':
      return {
        container: spacing.xl,
        section: spacing.lg,
        card: spacing.md,
        element: spacing.sm,
      };
    case 'lg':
      return {
        container: spacing.lg,
        section: spacing.md,
        card: spacing.md,
        element: spacing.sm,
      };
    case 'md':
      return {
        container: spacing.md,
        section: spacing.sm,
        card: spacing.sm,
        element: spacing.xs,
      };
    default:
      return {
        container: spacing.sm,
        section: spacing.xs,
        card: spacing.xs,
        element: spacing.xs,
      };
  }
}

/**
 * Hook for responsive font sizes
 */
export function useResponsiveFontSize() {
  const { currentBreakpoint } = useBreakpoint();

  const fontSizes = {
    h1: {
      sm: '1.875rem', // 30px
      md: '2.25rem', // 36px
      lg: '3rem', // 48px
      xl: '3.75rem', // 60px
      '2xl': '4.5rem', // 72px
    },
    h2: {
      sm: '1.5rem', // 24px
      md: '1.875rem', // 30px
      lg: '2.25rem', // 36px
      xl: '3rem', // 48px
      '2xl': '3.75rem', // 60px
    },
    h3: {
      sm: '1.25rem', // 20px
      md: '1.5rem', // 24px
      lg: '1.875rem', // 30px
      xl: '2.25rem', // 36px
      '2xl': '3rem', // 48px
    },
    body: {
      sm: '0.875rem', // 14px
      md: '1rem', // 16px
      lg: '1.125rem', // 18px
      xl: '1.25rem', // 20px
      '2xl': '1.5rem', // 24px
    },
  };

  return {
    h1: fontSizes.h1[currentBreakpoint],
    h2: fontSizes.h2[currentBreakpoint],
    h3: fontSizes.h3[currentBreakpoint],
    body: fontSizes.body[currentBreakpoint],
  };
}

/**
 * Hook for responsive navigation behavior
 */
export function useResponsiveNavigation() {
  const { isMobile, isTablet } = useBreakpoint();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Close mobile menu when screen size changes to desktop
  useEffect(() => {
    if (!isMobile && !isTablet) {
      setIsMobileMenuOpen(false);
    }
  }, [isMobile, isTablet]);

  return {
    isMobile,
    isTablet,
    showMobileMenu: isMobile || isTablet,
    isMobileMenuOpen,
    toggleMobileMenu: () => setIsMobileMenuOpen(!isMobileMenuOpen),
    closeMobileMenu: () => setIsMobileMenuOpen(false),
  };
}

/**
 * Hook for responsive table behavior
 */
export function useResponsiveTable() {
  const { isMobile, isTablet } = useBreakpoint();

  return {
    shouldStack: isMobile,
    shouldScroll: isMobile || isTablet,
    showAllColumns: !isMobile && !isTablet,
    maxVisibleColumns: isMobile ? 2 : isTablet ? 4 : undefined,
  };
}

/**
 * Hook for responsive modal behavior
 */
export function useResponsiveModal() {
  const { isMobile, windowSize } = useBreakpoint();

  return {
    isFullScreen: isMobile,
    maxWidth: isMobile ? '100%' : windowSize.width < 768 ? '90%' : '80%',
    maxHeight: isMobile ? '100%' : '90vh',
    padding: isMobile ? '1rem' : '2rem',
  };
}

/**
 * Hook for responsive card layout
 */
export function useResponsiveCardLayout() {
  const { currentBreakpoint } = useBreakpoint();

  const layouts = {
    sm: {
      columns: 1,
      gap: '1rem',
      padding: '1rem',
    },
    md: {
      columns: 2,
      gap: '1.5rem',
      padding: '1.5rem',
    },
    lg: {
      columns: 3,
      gap: '2rem',
      padding: '2rem',
    },
    xl: {
      columns: 4,
      gap: '2rem',
      padding: '2rem',
    },
    '2xl': {
      columns: 4,
      gap: '3rem',
      padding: '3rem',
    },
  };

  return layouts[currentBreakpoint];
}

/**
 * Hook for responsive dashboard layout
 */
export function useResponsiveDashboard() {
  const { isMobile, isTablet, isDesktop } = useBreakpoint();
  const spacing = useResponsiveSpacing();
  const cardLayout = useResponsiveCardLayout();

  return {
    layout: {
      isMobile,
      isTablet,
      isDesktop,
      showSidebar: isDesktop,
      stackVertically: isMobile,
    },
    spacing,
    cards: cardLayout,
    navigation: {
      orientation: isMobile ? 'vertical' : 'horizontal',
      showLabels: !isMobile,
      compact: isMobile,
    },
    content: {
      maxWidth: isMobile ? '100%' : isTablet ? '90%' : '80%',
      padding: spacing.container,
    },
  };
}

/**
 * Hook for touch device detection
 */
export function useTouchDevice() {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const checkTouchDevice = () => {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    };

    setIsTouchDevice(checkTouchDevice());
  }, []);

  return {
    isTouchDevice,
    touchTargetSize: isTouchDevice ? '48px' : '44px',
    tapHighlight: isTouchDevice ? 'rgba(0, 0, 0, 0.1)' : 'none',
  };
}

// Export all hooks
export default {
  useBreakpoint,
  useMediaQuery,
  useResponsiveGrid,
  useResponsiveValue,
  useResponsiveSpacing,
  useResponsiveFontSize,
  useResponsiveNavigation,
  useResponsiveTable,
  useResponsiveModal,
  useResponsiveCardLayout,
  useResponsiveDashboard,
  useTouchDevice,
};
