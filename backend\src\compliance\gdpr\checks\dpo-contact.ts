import puppeteer, { <PERSON>, <PERSON><PERSON><PERSON> } from 'puppeteer';
import { GdprCheckResult, Evidence, Recommendation } from '../types';

export interface DpoContactCheckConfig {
  targetUrl: string;
  timeout: number;
}

export class DpoContactCheck {
  /**
   * Check for Data Protection Officer contact information
   * REAL ANALYSIS - scans for DPO contact details
   */
  async performCheck(config: DpoContactCheckConfig): Promise<GdprCheckResult> {
    let browser: Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      });

      const page = await browser.newPage();
      await page.setUserAgent('GDPR-Compliance-Scanner/1.0');

      await page.goto(config.targetUrl, {
        waitUntil: 'networkidle2',
        timeout: config.timeout,
      });

      // Analyze for DPO contact information
      const dpoAnalysis = await this.analyzeDpoContact(page);

      // Score based on DPO information found
      if (dpoAnalysis.foundPatterns.length > 0) {
        score += 60;
        evidence.push({
          type: 'text',
          description: 'DPO/Privacy Officer mentioned',
          value: `Found: ${dpoAnalysis.foundPatterns.join(', ')}`,
        });
      } else {
        evidence.push({
          type: 'text',
          description: 'No DPO/Privacy Officer information found',
          value: 'DPO contact information not detected',
        });
      }

      if (dpoAnalysis.hasContactInfo) {
        score += 40;
        evidence.push({
          type: 'text',
          description: 'DPO contact information available',
          value: dpoAnalysis.contactDetails,
        });
      } else {
        evidence.push({
          type: 'text',
          description: 'DPO contact information missing',
          value: 'No specific contact details found for DPO',
        });
      }

      // Check for EU representative information (for non-EU companies)
      const euRepAnalysis = await this.analyzeEuRepresentative(page);

      if (euRepAnalysis.found) {
        score += 20; // Bonus for EU representative
        evidence.push({
          type: 'text',
          description: 'EU Representative information found',
          value: euRepAnalysis.details,
        });
      }

      const passed = score >= 60;

      return {
        ruleId: 'GDPR-015',
        ruleName: 'Data Protection Officer/EU Representative',
        category: 'organizational',
        passed,
        score,
        weight: 3,
        severity: 'medium',
        evidence,
        recommendations: this.generateRecommendations(dpoAnalysis, score),
        manualReviewRequired: false,
      };
    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Analyze page for DPO contact information
   */
  private async analyzeDpoContact(page: Page): Promise<{
    foundPatterns: string[];
    hasContactInfo: boolean;
    contactDetails: string;
  }> {
    return await page.evaluate(() => {
      const text = document.body.textContent?.toLowerCase() || '';

      const dpoPatterns = [
        'data protection officer',
        'dpo',
        'privacy officer',
        'data protection contact',
        'privacy contact',
        'chief privacy officer',
        'data protection manager',
      ];

      const foundPatterns = dpoPatterns.filter((pattern) => text.includes(pattern));

      // Look for contact information near DPO mentions
      let hasContactInfo = false;
      let contactDetails = '';

      if (foundPatterns.length > 0) {
        // Check for email addresses
        const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
        const emails = text.match(emailPattern) || [];

        // Check for phone numbers
        const phonePattern = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
        const phones = text.match(phonePattern) || [];

        const contactMethods: string[] = [];

        if (emails.length > 0) {
          contactMethods.push(`${emails.length} email address(es)`);
        }

        if (phones.length > 0) {
          contactMethods.push(`${phones.length} phone number(s)`);
        }

        // Look for postal addresses
        const addressKeywords = ['address', 'street', 'avenue', 'road', 'suite', 'floor'];
        const hasAddress = addressKeywords.some((keyword) => text.includes(keyword));

        if (hasAddress) {
          contactMethods.push('postal address');
        }

        if (contactMethods.length > 0) {
          hasContactInfo = true;
          contactDetails = `Contact methods found: ${contactMethods.join(', ')}`;
        }
      }

      return {
        foundPatterns,
        hasContactInfo,
        contactDetails: contactDetails || 'No contact information found',
      };
    });
  }

  /**
   * Analyze for EU representative information
   */
  private async analyzeEuRepresentative(page: Page): Promise<{
    found: boolean;
    details: string;
  }> {
    return await page.evaluate(() => {
      const text = document.body.textContent?.toLowerCase() || '';

      const euRepPatterns = [
        'eu representative',
        'european representative',
        'representative in the eu',
        'representative in the european union',
        'eu rep',
        'european union representative',
      ];

      const foundPatterns = euRepPatterns.filter((pattern) => text.includes(pattern));

      return {
        found: foundPatterns.length > 0,
        details:
          foundPatterns.length > 0
            ? `EU representative mentioned: ${foundPatterns.join(', ')}`
            : 'No EU representative information found',
      };
    });
  }

  /**
   * Generate recommendations for DPO compliance
   */
  private generateRecommendations(
    dpoAnalysis: { foundPatterns: string[]; hasContactInfo: boolean },
    score: number,
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (score < 60) {
      if (dpoAnalysis.foundPatterns.length === 0) {
        recommendations.push({
          priority: 1,
          title: 'Add DPO contact information',
          description: 'Include Data Protection Officer contact details in privacy policy',
          implementation: 'Add DPO contact section to privacy policy or contact page',
          effort: 'minimal',
          impact: 'medium',
        });
      } else if (!dpoAnalysis.hasContactInfo) {
        recommendations.push({
          priority: 1,
          title: 'Provide DPO contact details',
          description: 'Include specific contact information for the Data Protection Officer',
          implementation: 'Add email, phone, or postal address for DPO contact',
          effort: 'minimal',
          impact: 'medium',
        });
      }
    }

    if (score < 40) {
      recommendations.push({
        priority: 2,
        title: 'Consider EU representative appointment',
        description: 'Non-EU companies may need to appoint an EU representative',
        implementation: 'Evaluate need for EU representative under GDPR Article 27',
        effort: 'moderate',
        impact: 'medium',
      });
    }

    recommendations.push({
      priority: 3,
      title: 'Ensure DPO accessibility',
      description: 'Make DPO contact information easily accessible to data subjects',
      implementation: 'Include DPO contact in privacy policy and make it prominent',
      effort: 'minimal',
      impact: 'low',
    });

    return recommendations;
  }

  /**
   * Create error result
   */
  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-015',
      ruleName: 'Data Protection Officer/EU Representative',
      category: 'organizational',
      passed: false,
      score: 0,
      weight: 3,
      severity: 'medium',
      evidence: [
        {
          type: 'text',
          description: 'DPO check failed',
          value: error instanceof Error ? error.message : 'Unknown error',
        },
      ],
      recommendations: [
        {
          priority: 1,
          title: 'Add DPO information',
          description: 'Include Data Protection Officer contact details',
          implementation: 'Update privacy policy with DPO contact information',
          effort: 'minimal',
          impact: 'medium',
        },
      ],
      manualReviewRequired: false,
    };
  }
}
