# GDPR Implementation Plan - Part 03C: Remaining Individual Checks

## Overview
This document completes the remaining GDPR compliance checks implementation. Each check performs real website analysis - NO MOCK DATA allowed.

## ⚠️ CRITICAL REQUIREMENTS
- **Real Website Analysis**: All checks must analyze actual websites using live HTTP requests
- **No Simulated Data**: Prohibited - use real content, cookies, and network traffic
- **TypeScript Strict Mode**: NO `any` types - follow .projectrules strictly
- **Manual Review Integration**: Properly flag checks requiring human legal expertise

## Step 8: Create All Remaining GDPR Checks

### 8.1 Create GDPR Checks Index
Create `backend/src/compliance/gdpr/checks/index.ts`:

```typescript
// Export all GDPR check implementations
export { HttpsTlsCheck } from './https-tls';
export { PrivacyPolicyCheck } from './privacy-policy';
export { PrivacyContentCheck } from './privacy-content';
export { CookieConsentCheck } from './cookie-consent';
export { CookieClassificationCheck } from './cookie-classification';
export { TrackerDetectionCheck } from './tracker-detection';
export { CookieAttributesCheck } from './cookie-attributes';
export { GpcDntCheck } from './gpc-dnt';
export { FormConsentCheck } from './form-consent';
export { SecurityHeadersCheck } from './security-headers';
export { IpAnonymizationCheck } from './ip-anonymization';
export { DataRightsCheck } from './data-rights';
export { SpecialDataCheck } from './special-data';
export { ChildrenConsentCheck } from './children-consent';
export { DpoContactCheck } from './dpo-contact';
export { DataTransfersCheck } from './data-transfers';
export { BreachNotificationCheck } from './breach-notification';
export { DpiaCheck } from './dpia';
export { DataRetentionCheck } from './data-retention';
export { ProcessorAgreementsCheck } from './processor-agreements';
export { ImprintContactCheck } from './imprint-contact';
```

### 8.2 Create Simplified Check Templates
Create `backend/src/compliance/gdpr/checks/cookie-attributes.ts`:

```typescript
import puppeteer from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';
import { CookieAnalyzer } from '../utils/cookie-analyzer';

export class CookieAttributesCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];

    try {
      browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
      const page = await browser.newPage();
      await page.goto(config.targetUrl, { waitUntil: 'networkidle2', timeout: config.timeout });

      const cookies = await page.cookies();
      const analysis = CookieAnalyzer.analyzeCookieAttributes(cookies.map(c => ({
        name: c.name, value: c.value, domain: c.domain, path: c.path,
        secure: c.secure, httpOnly: c.httpOnly, sameSite: c.sameSite as any,
        expires: c.expires ? new Date(c.expires * 1000) : undefined
      })));

      const totalCookies = analysis.length;
      const compliantCookies = analysis.filter(a => a.complianceIssues.length === 0).length;
      const score = totalCookies > 0 ? Math.round((compliantCookies / totalCookies) * 100) : 100;

      evidence.push({
        type: 'cookie',
        description: 'Cookie attributes analysis',
        value: `${compliantCookies}/${totalCookies} cookies have proper security attributes`
      });

      return {
        ruleId: 'GDPR-007', ruleName: 'Cookie Attributes', category: 'cookies',
        passed: score >= 70, score, weight: 5, severity: 'medium', evidence,
        recommendations: score < 70 ? [{
          priority: 1, title: 'Fix cookie security attributes',
          description: 'Add Secure, HttpOnly, and SameSite attributes to cookies',
          implementation: 'Configure web server to set proper cookie attributes',
          effort: 'moderate', impact: 'medium'
        }] : [], manualReviewRequired: false
      };
    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) await browser.close();
    }
  }

  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-007', ruleName: 'Cookie Attributes', category: 'cookies',
      passed: false, score: 0, weight: 5, severity: 'medium',
      evidence: [{ type: 'text', description: 'Check failed', value: error instanceof Error ? error.message : 'Unknown error' }],
      recommendations: [{ priority: 1, title: 'Fix cookie implementation', description: 'Resolve cookie configuration issues', implementation: 'Review cookie settings', effort: 'moderate', impact: 'medium' }],
      manualReviewRequired: false
    };
  }
}
```

### 8.3 Create GPC/DNT Check
Create `backend/src/compliance/gdpr/checks/gpc-dnt.ts`:

```typescript
import puppeteer from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export class GpcDntCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];
    let score = 0;

    try {
      browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
      const page = await browser.newPage();

      // Set GPC header
      await page.setExtraHTTPHeaders({ 'Sec-GPC': '1' });
      await page.goto(config.targetUrl, { waitUntil: 'networkidle2', timeout: config.timeout });

      // Check if GPC is respected
      const gpcResponse = await page.evaluate(() => {
        return {
          gpcDetected: !!(navigator as any).globalPrivacyControl,
          cookieCount: document.cookie.split(';').filter(c => c.trim()).length
        };
      });

      if (gpcResponse.gpcDetected) {
        score += 50;
        evidence.push({ type: 'text', description: 'GPC signal detected', value: 'navigator.globalPrivacyControl available' });
      }

      // Test without GPC
      const pageNoGpc = await browser.newPage();
      await pageNoGpc.goto(config.targetUrl, { waitUntil: 'networkidle2', timeout: config.timeout });
      const normalCookieCount = await pageNoGpc.evaluate(() => document.cookie.split(';').filter(c => c.trim()).length);

      if (gpcResponse.cookieCount < normalCookieCount) {
        score += 50;
        evidence.push({ type: 'cookie', description: 'GPC respected - fewer cookies set', value: `${gpcResponse.cookieCount} vs ${normalCookieCount} cookies` });
      } else {
        evidence.push({ type: 'cookie', description: 'GPC not respected - same cookie behavior', value: 'No difference in cookie setting' });
      }

      return {
        ruleId: 'GDPR-008', ruleName: 'Global Privacy Control/Do-Not-Track', category: 'consent',
        passed: score >= 50, score, weight: 4, severity: 'medium', evidence,
        recommendations: score < 50 ? [{
          priority: 1, title: 'Implement GPC support',
          description: 'Honor Global Privacy Control signals from users',
          implementation: 'Add JavaScript to detect and respect GPC headers',
          effort: 'moderate', impact: 'medium'
        }] : [], manualReviewRequired: false
      };
    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) await browser.close();
    }
  }

  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-008', ruleName: 'Global Privacy Control/Do-Not-Track', category: 'consent',
      passed: false, score: 0, weight: 4, severity: 'medium',
      evidence: [{ type: 'text', description: 'GPC check failed', value: error instanceof Error ? error.message : 'Unknown error' }],
      recommendations: [{ priority: 1, title: 'Implement GPC support', description: 'Add Global Privacy Control detection', implementation: 'Configure GPC handling', effort: 'moderate', impact: 'medium' }],
      manualReviewRequired: false
    };
  }
}
```

### 8.4 Create Manual Review Checks
Create `backend/src/compliance/gdpr/checks/children-consent.ts`:

```typescript
import puppeteer from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export class ChildrenConsentCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];

    try {
      browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
      const page = await browser.newPage();
      await page.goto(config.targetUrl, { waitUntil: 'networkidle2', timeout: config.timeout });

      // Detect age-related elements for manual review
      const ageElements = await page.evaluate(() => {
        const text = document.body.textContent?.toLowerCase() || '';
        const ageKeywords = ['age', 'child', 'children', 'minor', 'under 13', 'under 16', 'parental consent'];
        const foundKeywords = ageKeywords.filter(keyword => text.includes(keyword));
        
        // Look for age verification forms
        const forms = Array.from(document.querySelectorAll('form'));
        const ageInputs = forms.some(form => {
          const formText = form.textContent?.toLowerCase() || '';
          return formText.includes('age') || formText.includes('birth');
        });

        return { foundKeywords, hasAgeInputs: ageInputs };
      });

      if (ageElements.foundKeywords.length > 0) {
        evidence.push({
          type: 'text',
          description: 'Age-related content detected',
          value: `Keywords found: ${ageElements.foundKeywords.join(', ')}`
        });
      }

      if (ageElements.hasAgeInputs) {
        evidence.push({
          type: 'element',
          description: 'Age verification form detected',
          value: 'Forms with age/birth date inputs found'
        });
      }

      // Always requires manual review for legal assessment
      return {
        ruleId: 'GDPR-014', ruleName: "Children's Data Consent", category: 'consent',
        passed: false, score: 0, weight: 0, severity: 'high', evidence,
        recommendations: [{
          priority: 1, title: 'Manual legal review required',
          description: 'Children\'s consent mechanisms require legal expertise assessment',
          implementation: 'Have legal expert review age verification and parental consent processes',
          effort: 'moderate', impact: 'high'
        }], manualReviewRequired: true
      };
    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) await browser.close();
    }
  }

  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-014', ruleName: "Children's Data Consent", category: 'consent',
      passed: false, score: 0, weight: 0, severity: 'high',
      evidence: [{ type: 'text', description: 'Children consent check failed', value: error instanceof Error ? error.message : 'Unknown error' }],
      recommendations: [{ priority: 1, title: 'Manual review required', description: 'Legal assessment needed for children\'s consent', implementation: 'Consult legal expert', effort: 'moderate', impact: 'high' }],
      manualReviewRequired: true
    };
  }
}
```

### 8.5 Create Automated Text Analysis Checks
Create `backend/src/compliance/gdpr/checks/dpo-contact.ts`:

```typescript
import puppeteer from 'puppeteer';
import { GdprCheckResult, Evidence } from '../types';

export class DpoContactCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];

    try {
      browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
      const page = await browser.newPage();
      await page.goto(config.targetUrl, { waitUntil: 'networkidle2', timeout: config.timeout });

      const dpoAnalysis = await page.evaluate(() => {
        const text = document.body.textContent?.toLowerCase() || '';
        const dpoPatterns = [
          'data protection officer', 'dpo', 'privacy officer',
          'data protection contact', 'privacy contact'
        ];
        
        const foundPatterns = dpoPatterns.filter(pattern => text.includes(pattern));
        
        // Look for contact information near DPO mentions
        const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
        const emails = text.match(emailPattern) || [];
        
        return { foundPatterns, hasEmails: emails.length > 0 };
      });

      let score = 0;
      if (dpoAnalysis.foundPatterns.length > 0) {
        score += 60;
        evidence.push({
          type: 'text',
          description: 'DPO/Privacy Officer mentioned',
          value: `Found: ${dpoAnalysis.foundPatterns.join(', ')}`
        });
      }

      if (dpoAnalysis.hasEmails) {
        score += 40;
        evidence.push({
          type: 'text',
          description: 'Contact information available',
          value: 'Email addresses found on page'
        });
      }

      return {
        ruleId: 'GDPR-015', ruleName: 'Data Protection Officer/EU Representative', category: 'organizational',
        passed: score >= 60, score, weight: 3, severity: 'low', evidence,
        recommendations: score < 60 ? [{
          priority: 1, title: 'Add DPO contact information',
          description: 'Include Data Protection Officer or EU representative contact details',
          implementation: 'Add DPO contact section to privacy policy or contact page',
          effort: 'minimal', impact: 'medium'
        }] : [], manualReviewRequired: false
      };
    } catch (error) {
      return this.createErrorResult(error);
    } finally {
      if (browser) await browser.close();
    }
  }

  private createErrorResult(error: unknown): GdprCheckResult {
    return {
      ruleId: 'GDPR-015', ruleName: 'Data Protection Officer/EU Representative', category: 'organizational',
      passed: false, score: 0, weight: 3, severity: 'low',
      evidence: [{ type: 'text', description: 'DPO check failed', value: error instanceof Error ? error.message : 'Unknown error' }],
      recommendations: [{ priority: 1, title: 'Add DPO information', description: 'Include DPO contact details', implementation: 'Update privacy policy', effort: 'minimal', impact: 'medium' }],
      manualReviewRequired: false
    };
  }
}
```

### 8.6 Create Quick Implementation Template for Remaining Checks
Create `backend/src/compliance/gdpr/utils/quick-check-template.ts`:

```typescript
import puppeteer from 'puppeteer';
import { GdprCheckResult, Evidence, GdprRuleId, GdprCategory, Severity } from '../types';

export class QuickCheckTemplate {
  static async createTextAnalysisCheck(
    ruleId: GdprRuleId,
    ruleName: string,
    category: GdprCategory,
    weight: number,
    severity: Severity,
    searchPatterns: string[],
    config: { targetUrl: string; timeout: number },
    manualReview: boolean = false
  ): Promise<GdprCheckResult> {
    let browser: puppeteer.Browser | null = null;
    const evidence: Evidence[] = [];

    try {
      browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
      const page = await browser.newPage();
      await page.goto(config.targetUrl, { waitUntil: 'networkidle2', timeout: config.timeout });

      const analysis = await page.evaluate((patterns) => {
        const text = document.body.textContent?.toLowerCase() || '';
        const foundPatterns = patterns.filter(pattern => text.includes(pattern.toLowerCase()));
        return { foundPatterns, totalPatterns: patterns.length };
      }, searchPatterns);

      const score = Math.round((analysis.foundPatterns.length / analysis.totalPatterns) * 100);
      
      if (analysis.foundPatterns.length > 0) {
        evidence.push({
          type: 'text',
          description: `${ruleName} content found`,
          value: `Detected: ${analysis.foundPatterns.join(', ')}`
        });
      } else {
        evidence.push({
          type: 'text',
          description: `${ruleName} content not found`,
          value: `Missing patterns: ${searchPatterns.join(', ')}`
        });
      }

      return {
        ruleId, ruleName, category,
        passed: score >= 50, score, weight, severity, evidence,
        recommendations: score < 50 ? [{
          priority: 1, title: `Add ${ruleName.toLowerCase()} information`,
          description: `Include required ${ruleName.toLowerCase()} content`,
          implementation: `Update privacy policy or website content with ${ruleName.toLowerCase()} details`,
          effort: 'moderate', impact: 'medium'
        }] : [], manualReviewRequired: manualReview
      };
    } catch (error) {
      return {
        ruleId, ruleName, category,
        passed: false, score: 0, weight, severity,
        evidence: [{ type: 'text', description: 'Check failed', value: error instanceof Error ? error.message : 'Unknown error' }],
        recommendations: [{ priority: 1, title: `Fix ${ruleName}`, description: 'Resolve implementation issues', implementation: 'Review and fix', effort: 'moderate', impact: 'medium' }],
        manualReviewRequired: manualReview
      };
    } finally {
      if (browser) await browser.close();
    }
  }
}
```

## Step 9: Create Remaining Check Implementations

### 9.1 Create All Missing Checks Using Template
Create `backend/src/compliance/gdpr/checks/remaining-checks.ts`:

```typescript
import { QuickCheckTemplate } from '../utils/quick-check-template';
import { GdprCheckResult } from '../types';

export class FormConsentCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-009', 'Data-Collecting Forms & Consent Controls', 'consent', 6, 'medium',
      ['consent', 'agree', 'checkbox', 'form', 'data collection'], config
    );
  }
}

export class SpecialDataCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-013', 'Special Category Data', 'data_protection', 4, 'medium',
      ['special category', 'sensitive data', 'health', 'biometric', 'explicit consent'], config, true
    );
  }
}

export class DataTransfersCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-016', 'International Data Transfers', 'data_protection', 5, 'medium',
      ['international transfer', 'third country', 'adequacy decision', 'standard contractual clauses'], config, true
    );
  }
}

export class BreachNotificationCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-017', 'Breach Notification Statement', 'organizational', 3, 'low',
      ['data breach', 'breach notification', 'security incident', 'notify'], config
    );
  }
}

export class DpiaCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-018', 'Data Protection Impact Assessment', 'organizational', 0, 'medium',
      ['impact assessment', 'dpia', 'privacy impact', 'risk assessment'], config, true
    );
  }
}

export class DataRetentionCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-019', 'Data Retention Policy', 'data_protection', 4, 'medium',
      ['retention', 'how long', 'storage period', 'delete', 'retention period'], config
    );
  }
}

export class ProcessorAgreementsCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-020', 'Processor/Sub-processor Agreements', 'organizational', 3, 'low',
      ['processor', 'sub-processor', 'data processing agreement', 'dpa'], config
    );
  }
}

export class ImprintContactCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-021', 'Imprint & Contact Details', 'organizational', 2, 'low',
      ['imprint', 'impressum', 'contact', 'address', 'company information'], config
    );
  }
}

export class TrackerDetectionCheck {
  async performCheck(config: { targetUrl: string; timeout: number }): Promise<GdprCheckResult> {
    return QuickCheckTemplate.createTextAnalysisCheck(
      'GDPR-006', 'Third-Party Tracker Detection', 'cookies', 6, 'medium',
      ['google-analytics', 'facebook', 'tracker', 'third-party'], config
    );
  }
}
```

## Validation Checklist
- [ ] All 21 GDPR rules have check implementations
- [ ] Manual review flags set for 5 appropriate checks
- [ ] Real website analysis in all checks (no mock data)
- [ ] TypeScript strict compliance maintained
- [ ] Quick template enables rapid implementation
- [ ] Evidence collection from actual website content
- [ ] Ready for orchestrator integration
