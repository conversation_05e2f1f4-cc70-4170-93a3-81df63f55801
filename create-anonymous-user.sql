-- Create anonymous user for GDPR scans
-- This fixes the foreign key constraint violation

-- Check if anonymous user already exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM users WHERE id = '00000000-0000-0000-0000-000000000000') THEN
        -- Create anonymous user
        INSERT INTO users (
            id, 
            email, 
            username, 
            first_name, 
            last_name, 
            is_active, 
            is_verified, 
            role, 
            created_at, 
            updated_at
        ) VALUES (
            '00000000-0000-0000-0000-000000000000', 
            '<EMAIL>', 
            'anonymous', 
            'Anonymous', 
            'User', 
            true, 
            false, 
            'user', 
            NOW(), 
            NOW()
        );
        
        RAISE NOTICE 'Anonymous user created successfully with UUID: 00000000-0000-0000-0000-000000000000';
    ELSE
        RAISE NOTICE 'Anonymous user already exists, skipping creation';
    END IF;
END $$;
