'use client';

/**
 * WCAG Dashboard Layout
 * Provides consistent layout and navigation for WCAG compliance pages
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { 
  PlayCircle, 
  History, 
  FileText, 
  Settings,
  Assessment,
  TrendingUp,
  Shield
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface WcagLayoutProps {
  children: React.ReactNode;
}

/**
 * WCAG Dashboard Layout Component
 */
export default function WcagLayout({ children }: WcagLayoutProps) {
  const pathname = usePathname();

  const navigationItems = [
    {
      label: 'Dashboard',
      href: '/dashboard/wcag',
      icon: <Assessment className="h-4 w-4" />,
      description: 'Overview and quick actions'
    },
    {
      label: 'Start Scan',
      href: '/dashboard/wcag/scan',
      icon: <PlayCircle className="h-4 w-4" />,
      description: 'Configure and run new scans'
    },
    {
      label: 'Scan History',
      href: '/dashboard/wcag/history',
      icon: <History className="h-4 w-4" />,
      description: 'View past scan results'
    },
    {
      label: 'Reports',
      href: '/dashboard/wcag/reports',
      icon: <FileText className="h-4 w-4" />,
      description: 'Generate and export reports'
    },
    {
      label: 'Settings',
      href: '/dashboard/wcag/settings',
      icon: <Settings className="h-4 w-4" />,
      description: 'Configure scan preferences'
    }
  ];

  const isActivePath = (href: string) => {
    if (href === '/dashboard/wcag') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardContent className="p-6">
                {/* Header */}
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h2 className="font-semibold text-gray-900 dark:text-white">
                      WCAG Compliance
                    </h2>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Accessibility Testing
                    </p>
                  </div>
                </div>

                {/* Navigation */}
                <nav className="space-y-2">
                  {navigationItems.map((item) => {
                    const isActive = isActivePath(item.href);
                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className={`
                          flex items-center gap-3 p-3 rounded-lg transition-colors
                          ${isActive 
                            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800' 
                            : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
                          }
                        `}
                      >
                        <div className={`
                          ${isActive 
                            ? 'text-blue-600 dark:text-blue-400' 
                            : 'text-gray-400 dark:text-gray-500'
                          }
                        `}>
                          {item.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className={`
                            font-medium text-sm
                            ${isActive 
                              ? 'text-blue-700 dark:text-blue-300' 
                              : 'text-gray-900 dark:text-white'
                            }
                          `}>
                            {item.label}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {item.description}
                          </div>
                        </div>
                        {isActive && (
                          <Badge variant="default" className="text-xs">
                            Active
                          </Badge>
                        )}
                      </Link>
                    );
                  })}
                </nav>

                {/* Quick Stats */}
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    Quick Stats
                  </h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Total Scans</span>
                      <span className="font-medium text-gray-900 dark:text-white">--</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Avg Score</span>
                      <span className="font-medium text-gray-900 dark:text-white">--%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">Last Scan</span>
                      <span className="font-medium text-gray-900 dark:text-white">--</span>
                    </div>
                  </div>
                </div>

                {/* Help Section */}
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    Need Help?
                  </h3>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <FileText className="h-4 w-4 mr-2" />
                      Documentation
                    </Button>
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Best Practices
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
