'use client';

/**
 * WCAG Settings Page
 * Page for configuring WCAG scan preferences and settings
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Switch } from '@/components/ui/Switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { Slider } from '@/components/ui/Slider';
import { Alert, AlertDescription } from '@/components/ui/Alert';
import { 
  ArrowLeft, 
  Save, 
  RotateCcw,
  Settings,
  Globe,
  Clock,
  Shield,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';

/**
 * WCAG Settings Content Component
 */
const WcagSettingsContent: React.FC = () => {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [settings, setSettings] = useState({
    // Default scan settings
    defaultWcagVersion: '2.1',
    defaultComplianceLevel: 'AA',
    defaultPageLimit: 10,
    
    // Scan options
    enableContrastChecks: true,
    enableKeyboardChecks: true,
    enableFocusChecks: true,
    enableSemanticChecks: true,
    
    // Performance settings
    maxConcurrentScans: 3,
    scanTimeout: 300, // seconds
    retryAttempts: 3,
    
    // Notification settings
    emailNotifications: true,
    scanCompleteNotifications: true,
    errorNotifications: true,
    
    // Report settings
    defaultExportFormat: 'pdf',
    includeEvidence: true,
    includeRecommendations: true,
    includeManualReviewItems: false,
    
    // Advanced settings
    userAgent: 'ComplyChecker WCAG Scanner 1.0',
    viewportWidth: 1920,
    viewportHeight: 1080,
    enableJavaScript: true,
    waitForImages: true,
    followRedirects: true
  });
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // In a real implementation, load settings from API
      // For now, we'll use localStorage
      const savedSettings = localStorage.getItem('wcag-settings');
      if (savedSettings) {
        setSettings({ ...settings, ...JSON.parse(savedSettings) });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const handleBack = () => {
    router.push('/dashboard/wcag');
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
    setSaveSuccess(false);
    setError(null);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      // In a real implementation, save to API
      // For now, we'll use localStorage
      localStorage.setItem('wcag-settings', JSON.stringify(settings));
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      setSaveSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      setSettings({
        defaultWcagVersion: '2.1',
        defaultComplianceLevel: 'AA',
        defaultPageLimit: 10,
        enableContrastChecks: true,
        enableKeyboardChecks: true,
        enableFocusChecks: true,
        enableSemanticChecks: true,
        maxConcurrentScans: 3,
        scanTimeout: 300,
        retryAttempts: 3,
        emailNotifications: true,
        scanCompleteNotifications: true,
        errorNotifications: true,
        defaultExportFormat: 'pdf',
        includeEvidence: true,
        includeRecommendations: true,
        includeManualReviewItems: false,
        userAgent: 'ComplyChecker WCAG Scanner 1.0',
        viewportWidth: 1920,
        viewportHeight: 1080,
        enableJavaScript: true,
        waitForImages: true,
        followRedirects: true
      });
      setHasChanges(true);
      setSaveSuccess(false);
      setError(null);
    }
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              WCAG Settings
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Configure your WCAG compliance scan preferences
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges || saving}
          >
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Success Alert */}
      {saveSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>Settings saved successfully!</AlertDescription>
        </Alert>
      )}

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Default Scan Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Default Scan Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="wcag-version">Default WCAG Version</Label>
              <Select 
                value={settings.defaultWcagVersion} 
                onValueChange={(value) => handleSettingChange('defaultWcagVersion', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="2.1">WCAG 2.1</SelectItem>
                  <SelectItem value="2.2">WCAG 2.2</SelectItem>
                  <SelectItem value="3.0">WCAG 3.0 (Draft)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="compliance-level">Default Compliance Level</Label>
              <Select 
                value={settings.defaultComplianceLevel} 
                onValueChange={(value) => handleSettingChange('defaultComplianceLevel', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">Level A</SelectItem>
                  <SelectItem value="AA">Level AA</SelectItem>
                  <SelectItem value="AAA">Level AAA</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="page-limit">Default Page Limit</Label>
              <div className="mt-2">
                <Slider
                  value={[settings.defaultPageLimit]}
                  onValueChange={(value) => handleSettingChange('defaultPageLimit', value[0])}
                  max={100}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>1 page</span>
                  <span>{settings.defaultPageLimit} pages</span>
                  <span>100 pages</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Label>Default Scan Options</Label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="contrast-checks" className="text-sm">Contrast Checks</Label>
                  <Switch
                    id="contrast-checks"
                    checked={settings.enableContrastChecks}
                    onCheckedChange={(checked) => handleSettingChange('enableContrastChecks', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="keyboard-checks" className="text-sm">Keyboard Navigation</Label>
                  <Switch
                    id="keyboard-checks"
                    checked={settings.enableKeyboardChecks}
                    onCheckedChange={(checked) => handleSettingChange('enableKeyboardChecks', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="focus-checks" className="text-sm">Focus Indicators</Label>
                  <Switch
                    id="focus-checks"
                    checked={settings.enableFocusChecks}
                    onCheckedChange={(checked) => handleSettingChange('enableFocusChecks', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="semantic-checks" className="text-sm">Semantic Structure</Label>
                  <Switch
                    id="semantic-checks"
                    checked={settings.enableSemanticChecks}
                    onCheckedChange={(checked) => handleSettingChange('enableSemanticChecks', checked)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Performance Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="concurrent-scans">Max Concurrent Scans</Label>
              <div className="mt-2">
                <Slider
                  value={[settings.maxConcurrentScans]}
                  onValueChange={(value) => handleSettingChange('maxConcurrentScans', value[0])}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>1 scan</span>
                  <span>{settings.maxConcurrentScans} scans</span>
                  <span>10 scans</span>
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="scan-timeout">Scan Timeout (seconds)</Label>
              <Input
                id="scan-timeout"
                type="number"
                value={settings.scanTimeout}
                onChange={(e) => handleSettingChange('scanTimeout', parseInt(e.target.value))}
                min={60}
                max={1800}
              />
            </div>

            <div>
              <Label htmlFor="retry-attempts">Retry Attempts</Label>
              <Input
                id="retry-attempts"
                type="number"
                value={settings.retryAttempts}
                onChange={(e) => handleSettingChange('retryAttempts', parseInt(e.target.value))}
                min={0}
                max={10}
              />
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="email-notifications">Email Notifications</Label>
              <Switch
                id="email-notifications"
                checked={settings.emailNotifications}
                onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="scan-complete">Scan Complete Notifications</Label>
              <Switch
                id="scan-complete"
                checked={settings.scanCompleteNotifications}
                onCheckedChange={(checked) => handleSettingChange('scanCompleteNotifications', checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="error-notifications">Error Notifications</Label>
              <Switch
                id="error-notifications"
                checked={settings.errorNotifications}
                onCheckedChange={(checked) => handleSettingChange('errorNotifications', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Report Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Report Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="export-format">Default Export Format</Label>
              <Select 
                value={settings.defaultExportFormat} 
                onValueChange={(value) => handleSettingChange('defaultExportFormat', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF Report</SelectItem>
                  <SelectItem value="json">JSON Data</SelectItem>
                  <SelectItem value="csv">CSV Spreadsheet</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Default Export Options</Label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="include-evidence" className="text-sm">Include Evidence</Label>
                  <Switch
                    id="include-evidence"
                    checked={settings.includeEvidence}
                    onCheckedChange={(checked) => handleSettingChange('includeEvidence', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="include-recommendations" className="text-sm">Include Recommendations</Label>
                  <Switch
                    id="include-recommendations"
                    checked={settings.includeRecommendations}
                    onCheckedChange={(checked) => handleSettingChange('includeRecommendations', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="include-manual" className="text-sm">Include Manual Review Items</Label>
                  <Switch
                    id="include-manual"
                    checked={settings.includeManualReviewItems}
                    onCheckedChange={(checked) => handleSettingChange('includeManualReviewItems', checked)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="user-agent">User Agent</Label>
              <Input
                id="user-agent"
                value={settings.userAgent}
                onChange={(e) => handleSettingChange('userAgent', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="viewport-width">Viewport Width</Label>
              <Input
                id="viewport-width"
                type="number"
                value={settings.viewportWidth}
                onChange={(e) => handleSettingChange('viewportWidth', parseInt(e.target.value))}
                min={320}
                max={3840}
              />
            </div>
            <div>
              <Label htmlFor="viewport-height">Viewport Height</Label>
              <Input
                id="viewport-height"
                type="number"
                value={settings.viewportHeight}
                onChange={(e) => handleSettingChange('viewportHeight', parseInt(e.target.value))}
                min={240}
                max={2160}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-js">Enable JavaScript</Label>
              <Switch
                id="enable-js"
                checked={settings.enableJavaScript}
                onCheckedChange={(checked) => handleSettingChange('enableJavaScript', checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="wait-images">Wait for Images</Label>
              <Switch
                id="wait-images"
                checked={settings.waitForImages}
                onCheckedChange={(checked) => handleSettingChange('waitForImages', checked)}
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="follow-redirects">Follow Redirects</Label>
              <Switch
                id="follow-redirects"
                checked={settings.followRedirects}
                onCheckedChange={(checked) => handleSettingChange('followRedirects', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

/**
 * Main WCAG Settings Page with Provider
 */
export default function WcagSettingsPage() {
  return (
    <WcagProvider>
      <WcagSettingsContent />
    </WcagProvider>
  );
}
