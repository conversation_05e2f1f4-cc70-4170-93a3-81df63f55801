/**
 * WCAG Rule 7: Focus Visible - 2.4.7
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { FocusTracker } from '../utils/focus-tracker';
import { WcagEvidence } from '../types';

export class FocusVisibleCheck {
  private checkTemplate = new CheckTemplate();

  /**
   * Perform focus visible check - 100% automated
   */
  async performCheck(config: CheckConfig) {
    return this.checkTemplate.executeCheck(
      'WCAG-007',
      'Focus Visible',
      'operable',
      0.09,
      'AA',
      config,
      this.executeFocusVisibleCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );
  }

  /**
   * Execute focus visibility analysis
   */
  private async executeFocusVisibleCheck(page: Page, _config: CheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Get all focusable elements
    const focusableElements = await FocusTracker.getFocusableElements(page);

    let totalElements = 0;
    let passedElements = 0;

    // Test focus visibility for each element
    for (const element of focusableElements) {
      totalElements++;

      const focusIndicator = await FocusTracker.analyzeFocusVisibility(page, element);

      if (focusIndicator.hasVisibleIndicator) {
        passedElements++;

        evidence.push({
          type: 'measurement',
          description: 'Focus indicator is visible and meets requirements',
          value: `Type: ${focusIndicator.indicatorType}, Contrast: ${focusIndicator.contrastRatio || 'N/A'}:1`,
          selector: element.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Focus indicator not visible on ${element.selector}`);

        evidence.push({
          type: 'measurement',
          description: 'Focus indicator fails visibility requirements',
          value: focusIndicator.recommendation || 'No visible focus indicator',
          selector: element.selector,
          severity: 'error',
        });

        if (focusIndicator.recommendation) {
          recommendations.push(`${element.selector}: ${focusIndicator.recommendation}`);
        }
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Focus visibility analysis summary',
      value: `${passedElements}/${totalElements} focusable elements have visible focus indicators`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift('Add visible focus indicators to all focusable elements');
      recommendations.push('Ensure focus indicators have sufficient contrast (3:1 minimum)');
      recommendations.push('Use outline, border, or background changes for focus indication');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
