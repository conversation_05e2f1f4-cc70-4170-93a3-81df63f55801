'use client';

/**
 * WCAG Breadcrumb Navigation Component
 * Provides breadcrumb navigation for WCAG compliance pages
 */

import React from 'react';
import { usePathname } from 'next/navigation';
import { ComplianceBreadcrumb, BreadcrumbItem } from './ComplianceBreadcrumb';
import { 
  Home, 
  Shield, 
  Assessment, 
  PlayArrow, 
  History, 
  FileText, 
  Settings,
  Eye
} from 'lucide-react';

interface WcagBreadcrumbProps {
  className?: string;
  customItems?: BreadcrumbItem[];
}

/**
 * WCAG Breadcrumb Component
 */
export const WcagBreadcrumb: React.FC<WcagBreadcrumbProps> = ({
  className,
  customItems
}) => {
  const pathname = usePathname();

  // If custom items are provided, use them
  if (customItems) {
    return <ComplianceBreadcrumb items={customItems} className={className} />;
  }

  // Generate breadcrumb items based on current path
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    const items: BreadcrumbItem[] = [
      {
        label: 'Dashboard',
        href: '/dashboard',
        icon: <Home className="h-4 w-4" />
      },
      {
        label: 'Compliance',
        href: '/dashboard',
        icon: <Shield className="h-4 w-4" />
      }
    ];

    // WCAG base
    if (pathname.startsWith('/dashboard/wcag')) {
      items.push({
        label: 'WCAG',
        href: '/dashboard/wcag',
        icon: <Assessment className="h-4 w-4" />
      });

      // Specific WCAG pages
      if (pathname === '/dashboard/wcag/scan') {
        items.push({
          label: 'Start Scan',
          icon: <PlayArrow className="h-4 w-4" />,
          current: true
        });
      } else if (pathname === '/dashboard/wcag/history') {
        items.push({
          label: 'Scan History',
          icon: <History className="h-4 w-4" />,
          current: true
        });
      } else if (pathname === '/dashboard/wcag/reports') {
        items.push({
          label: 'Reports',
          icon: <FileText className="h-4 w-4" />,
          current: true
        });
      } else if (pathname === '/dashboard/wcag/settings') {
        items.push({
          label: 'Settings',
          icon: <Settings className="h-4 w-4" />,
          current: true
        });
      } else if (pathname.startsWith('/dashboard/wcag/scan/') && pathname !== '/dashboard/wcag/scan') {
        // Scan details page
        const scanId = pathname.split('/').pop();
        items.push({
          label: 'Scan History',
          href: '/dashboard/wcag/history',
          icon: <History className="h-4 w-4" />
        });
        items.push({
          label: `Scan ${scanId?.slice(0, 8)}...`,
          icon: <Eye className="h-4 w-4" />,
          current: true
        });
      } else if (pathname === '/dashboard/wcag') {
        // WCAG dashboard is current
        items[items.length - 1].current = true;
      }
    }

    return items;
  };

  const breadcrumbItems = generateBreadcrumbItems();

  return <ComplianceBreadcrumb items={breadcrumbItems} className={className} />;
};

/**
 * Hook to generate WCAG breadcrumb items
 */
export const useWcagBreadcrumb = (customItems?: BreadcrumbItem[]): BreadcrumbItem[] => {
  const pathname = usePathname();

  if (customItems) {
    return customItems;
  }

  const items: BreadcrumbItem[] = [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <Home className="h-4 w-4" />
    },
    {
      label: 'Compliance',
      href: '/dashboard',
      icon: <Shield className="h-4 w-4" />
    }
  ];

  if (pathname.startsWith('/dashboard/wcag')) {
    items.push({
      label: 'WCAG',
      href: '/dashboard/wcag',
      icon: <Assessment className="h-4 w-4" />
    });

    if (pathname === '/dashboard/wcag/scan') {
      items.push({
        label: 'Start Scan',
        icon: <PlayArrow className="h-4 w-4" />,
        current: true
      });
    } else if (pathname === '/dashboard/wcag/history') {
      items.push({
        label: 'Scan History',
        icon: <History className="h-4 w-4" />,
        current: true
      });
    } else if (pathname === '/dashboard/wcag/reports') {
      items.push({
        label: 'Reports',
        icon: <FileText className="h-4 w-4" />,
        current: true
      });
    } else if (pathname === '/dashboard/wcag/settings') {
      items.push({
        label: 'Settings',
        icon: <Settings className="h-4 w-4" />,
        current: true
      });
    } else if (pathname.startsWith('/dashboard/wcag/scan/') && pathname !== '/dashboard/wcag/scan') {
      const scanId = pathname.split('/').pop();
      items.push({
        label: 'Scan History',
        href: '/dashboard/wcag/history',
        icon: <History className="h-4 w-4" />
      });
      items.push({
        label: `Scan ${scanId?.slice(0, 8)}...`,
        icon: <Eye className="h-4 w-4" />,
        current: true
      });
    } else if (pathname === '/dashboard/wcag') {
      items[items.length - 1].current = true;
    }
  }

  return items;
};

export default WcagBreadcrumb;
