# Comprehensive GDPR Website Compliance Checklist

_For each rule below, follow the “What to Check” guidance. See “Check Type” for whether it can be automated or requires manual review._

---

## 1. HTTPS / TLS Encryption  
**What to Check**  
- URL begins with `https://`  
- Valid certificate (expiry, chain)  
- Response header `Strict-Transport-Security` present  

**Check Type:** Automated

---

## 2. Privacy Policy Link & Presence  
**What to Check**  
- A footer or header link labeled “Privacy Policy” (or similar)  
- Link resolves to a page with privacy information  

**Check Type:** Automated (link detect) / Manual (content review)

---

## 3. Privacy Notice Content
**What to Check**
- Controller identity, contact, DPO/EU‑rep (if required)
- Processing purposes, data categories, recipients
- International transfers, retention periods
- Data subject rights & exercise instructions

**Check Type:** Automated (pattern matching) / Manual (legal compliance review)
**Technical Implementation:** Use NLP and pattern matching to detect presence of required sections, but manual review needed for legal adequacy

---

## 4. <PERSON><PERSON>  
**What to Check**  
- <PERSON> appears on first visit before non‑essential cookies set  
- Clear “Accept” and “Reject” (or equivalent) buttons  
- No pre‑ticked boxes  

**Check Type:** Automated (presence & behavior) / Manual (UX review)

---

## 5. Cookie Classification & Blocking  
**What to Check**  
- Essential cookies only before consent  
- Analytics/marketing cookies blocked until opt‑in  
- Granular categories (e.g. “Analytics”, “Marketing”)  

**Check Type:** Automated

---

## 6. Third‑Party Tracker Detection  
**What to Check**  
- Scripts or network calls to known tracker domains (Google, Facebook, DoubleClick, etc.)  
- Consent required before loading these scripts  

**Check Type:** Automated

---

## 7. Cookie Attributes  
**What to Check**  
- `Secure; HttpOnly` flags on cookies holding personal identifiers  
- `SameSite` set to `Lax` or `Strict` where appropriate  

**Check Type:** Automated

---

## 8. Global Privacy Control / Do‑Not‑Track
**What to Check**
- Honor `Sec-GPC: 1` HTTP header or `navigator.globalPrivacyControl` flag
- Block non‑essential cookies if detected

**Check Type:** Automated
**Technical Implementation:** Can detect GPC header/flag presence and test cookie blocking behavior programmatically

---

## 9. Data‑Collecting Forms & Consent Controls  
**What to Check**  
- Any form collecting personal data has an unchecked consent checkbox for non‑essential processing  
- Link to privacy policy adjacent to consent control  

**Check Type:** Automated (presence) / Manual (label wording)

---

## 10. Security Headers (Privacy by Design)  
**What to Check**  
- `Content-Security-Policy`  
- `X-Frame-Options`  
- `Referrer-Policy`  

**Check Type:** Automated

---

## 11. IP Address as Personal Data
**What to Check**
- Analytics scripts include IP anonymization (e.g. Google Analytics `anonymizeIp: true`)
- No un‑masked IP logging visible in client‑side code

**Check Type:** Automated
**Technical Implementation:** Parse JavaScript code to detect analytics configurations and IP anonymization settings

---

## 12. Data Subject Rights Links  
**What to Check**  
- Links or instructions for exercising rights (access, erasure, portability, objection)  
- Contact form or email for privacy requests  

**Check Type:** Automated (link detect) / Manual (procedural review)

---

## 13. Special Category Data (e.g. Health)  
**What to Check**  
- Explicit consent checkbox when collecting health or other sensitive data  
- Policy mentions processing of special categories and legal basis  

**Check Type:** Automated (checkbox detect) / Manual (policy text)

---

## 14. Children’s Data Consent  
**What to Check**  
- Age‑gate or clear statement of minimum age (typically 16, down to 13 by member state)  
- Parental consent mechanism if under threshold  

**Check Type:** Manual

---

## 15. Data Protection Officer / EU Representative
**What to Check**
- Policy lists a DPO or EU representative with contact details
- Email or postal address provided

**Check Type:** Automated
**Technical Implementation:** Use pattern matching to detect DPO/EU rep mentions and extract contact information

---

## 16. International Data Transfers
**What to Check**
- Policy mentions transfers outside EEA
- Safeguards: Standard Contractual Clauses, adequacy decisions, PDP frameworks

**Check Type:** Automated (detection) / Manual (legal adequacy)
**Technical Implementation:** Pattern matching for transfer-related keywords and safeguard mechanisms

---

## 17. Breach Notification Statement
**What to Check**
- Policy commits to notifying data subjects of personal data breaches

**Check Type:** Automated (pattern matching)
**Technical Implementation:** Search for breach notification commitments using keyword patterns

---

## 18. Data Protection Impact Assessment (DPIA)  
**What to Check**  
- Policy or site notes performance of DPIA for high‑risk processing  

**Check Type:** Manual

---

## 19. Data Retention Policy
**What to Check**
- Policy specifies retention periods or criteria

**Check Type:** Automated (pattern matching)
**Technical Implementation:** Search for retention period mentions and time-based criteria using NLP patterns

---

## 20. Processor / Sub‑processor Agreements
**What to Check**
- Policy mentions use of processors and their obligations (DPAs)

**Check Type:** Automated (pattern matching)
**Technical Implementation:** Search for processor/sub-processor mentions and DPA references using keyword patterns

---

## 21. Imprint & Contact Details  
**What to Check**  
- Organization name, address, registration number (where legally required)  

**Check Type:** Automated (presence) / Manual (completeness)

---

**Use this checklist to drive your automated scanner + manual review process.** Each “Automated” item can be coded (e.g. via TypeScript with Axios, Cheerio, Puppeteer); “Manual” items require human legal or UX verification.
