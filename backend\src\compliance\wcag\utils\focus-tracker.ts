/**
 * Focus Management Analysis Utilities
 * Provides comprehensive focus tracking and validation
 */

import { Page } from 'puppeteer';
import { FocusAnalysisResult } from '../types';

export interface FocusableElement {
  selector: string;
  tagName: string;
  type?: string;
  tabIndex: number;
  isVisible: boolean;
  hasVisibleFocus: boolean;
  ariaLabel?: string;
  role?: string;
  boundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface FocusOrderResult {
  elements: FocusableElement[];
  logicalOrder: boolean;
  skipLinks: FocusableElement[];
  focusTraps: string[];
  issues: string[];
}

export interface FocusVisibilityResult {
  hasVisibleIndicator: boolean;
  indicatorType: 'outline' | 'background' | 'border' | 'shadow' | 'none';
  contrastRatio?: number;
  meetsWcag: boolean;
  recommendation?: string;
}

export class FocusTracker {
  /**
   * Get all focusable elements on the page
   */
  static async getFocusableElements(page: Page): Promise<FocusableElement[]> {
    return await page.evaluate(() => {
      const focusableSelectors = [
        'a[href]',
        'button:not([disabled])',
        'input:not([disabled]):not([type="hidden"])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        '[tabindex]:not([tabindex="-1"])',
        '[contenteditable="true"]',
        'audio[controls]',
        'video[controls]',
        'iframe',
        'object',
        'embed',
        'area[href]',
        'summary',
      ];

      const elements: FocusableElement[] = [];

      focusableSelectors.forEach((selector) => {
        const nodeList = document.querySelectorAll(selector);
        nodeList.forEach((element, index) => {
          const rect = element.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(element);

          // Check if element is visible
          const isVisible =
            rect.width > 0 &&
            rect.height > 0 &&
            computedStyle.visibility !== 'hidden' &&
            computedStyle.display !== 'none';

          if (isVisible) {
            elements.push({
              selector: `${selector}:nth-of-type(${index + 1})`,
              tagName: element.tagName.toLowerCase(),
              type: (element as HTMLInputElement).type || undefined,
              tabIndex: (element as HTMLElement).tabIndex,
              isVisible,
              hasVisibleFocus: false, // Will be determined later
              ariaLabel: element.getAttribute('aria-label') || undefined,
              role: element.getAttribute('role') || undefined,
              boundingBox: {
                x: rect.x,
                y: rect.y,
                width: rect.width,
                height: rect.height,
              },
            });
          }
        });
      });

      return elements;
    });
  }

  /**
   * Analyze focus order and logical flow
   */
  static async analyzeFocusOrder(page: Page): Promise<FocusOrderResult> {
    const focusableElements = await this.getFocusableElements(page);

    // Test actual tab order
    const actualTabOrder: FocusableElement[] = [];
    const issues: string[] = [];

    // Start from the beginning of the page
    await page.keyboard.press('Tab');

    for (let i = 0; i < focusableElements.length && i < 50; i++) {
      try {
        const activeElement = await page.evaluate(() => {
          const active = document.activeElement;
          if (!active) return null;

          const rect = active.getBoundingClientRect();
          return {
            tagName: active.tagName.toLowerCase(),
            selector: active.id
              ? `#${active.id}`
              : active.className
                ? `.${active.className.split(' ')[0]}`
                : active.tagName.toLowerCase(),
            boundingBox: {
              x: rect.x,
              y: rect.y,
              width: rect.width,
              height: rect.height,
            },
          };
        });

        if (activeElement) {
          const matchingElement = focusableElements.find(
            (el) =>
              el.tagName === activeElement.tagName &&
              Math.abs(el.boundingBox.x - activeElement.boundingBox.x) < 5 &&
              Math.abs(el.boundingBox.y - activeElement.boundingBox.y) < 5,
          );

          if (matchingElement) {
            actualTabOrder.push(matchingElement);
          }
        }

        await page.keyboard.press('Tab');
        await new Promise((resolve) => setTimeout(resolve, 100)); // Small delay for focus to settle
      } catch (error) {
        console.error('Error during focus order analysis:', error);
        break;
      }
    }

    // Check for logical order (top-to-bottom, left-to-right)
    const logicalOrder = this.isLogicalOrder(actualTabOrder);

    // Find skip links
    const skipLinks = focusableElements.filter(
      (el) =>
        el.ariaLabel?.toLowerCase().includes('skip') || el.selector.toLowerCase().includes('skip'),
    );

    // Detect focus traps
    const focusTraps = await this.detectFocusTraps(page);

    if (!logicalOrder) {
      issues.push('Focus order does not follow logical reading order');
    }

    if (skipLinks.length === 0) {
      issues.push('No skip links found for keyboard navigation');
    }

    return {
      elements: actualTabOrder,
      logicalOrder,
      skipLinks,
      focusTraps,
      issues,
    };
  }

  /**
   * Check if focus order follows logical reading pattern
   */
  private static isLogicalOrder(elements: FocusableElement[]): boolean {
    for (let i = 1; i < elements.length; i++) {
      const prev = elements[i - 1];
      const curr = elements[i];

      // Check if current element is significantly above previous (wrong order)
      if (curr.boundingBox.y < prev.boundingBox.y - 20) {
        // Allow for same-line elements (within 20px vertically)
        if (curr.boundingBox.x < prev.boundingBox.x) {
          return false;
        }
      }
    }
    return true;
  }

  /**
   * Detect focus traps in the page
   */
  private static async detectFocusTraps(page: Page): Promise<string[]> {
    const traps: string[] = [];

    try {
      // Test for modal/dialog focus traps
      const modals = await page.evaluate(() => {
        const modalSelectors = [
          '[role="dialog"]',
          '[role="alertdialog"]',
          '.modal',
          '.dialog',
          '[aria-modal="true"]',
        ];

        const foundModals: string[] = [];
        modalSelectors.forEach((selector) => {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            foundModals.push(selector);
          }
        });

        return foundModals;
      });

      // If modals are present, test focus trapping
      for (const modalSelector of modals) {
        const isTrapped = await this.testFocusTrap(page, modalSelector);
        if (!isTrapped) {
          traps.push(`Modal ${modalSelector} does not properly trap focus`);
        }
      }
    } catch (error) {
      console.error('Error detecting focus traps:', error);
    }

    return traps;
  }

  /**
   * Test if a modal properly traps focus
   */
  private static async testFocusTrap(page: Page, modalSelector: string): Promise<boolean> {
    try {
      // Focus the modal
      await page.focus(modalSelector);

      // Try to tab out of the modal multiple times
      for (let i = 0; i < 10; i++) {
        await page.keyboard.press('Tab');

        const isStillInModal = await page.evaluate((selector) => {
          const modal = document.querySelector(selector);
          const activeElement = document.activeElement;
          return modal && modal.contains(activeElement);
        }, modalSelector);

        if (!isStillInModal) {
          return false; // Focus escaped the modal
        }
      }

      return true; // Focus stayed within modal
    } catch (error) {
      console.error('Error testing focus trap:', error);
      return false;
    }
  }

  /**
   * Analyze focus visibility indicators
   */
  static async analyzeFocusVisibility(
    page: Page,
    element: FocusableElement,
  ): Promise<FocusVisibilityResult> {
    try {
      await page.focus(element.selector);

      const focusStyles = await page.evaluate((selector) => {
        const element = document.querySelector(selector);
        if (!element) return null;

        const pseudoStyle = window.getComputedStyle(element, ':focus');

        return {
          outline: pseudoStyle.outline,
          outlineColor: pseudoStyle.outlineColor,
          outlineWidth: pseudoStyle.outlineWidth,
          backgroundColor: pseudoStyle.backgroundColor,
          borderColor: pseudoStyle.borderColor,
          borderWidth: pseudoStyle.borderWidth,
          boxShadow: pseudoStyle.boxShadow,
        };
      }, element.selector);

      if (!focusStyles) {
        return {
          hasVisibleIndicator: false,
          indicatorType: 'none',
          meetsWcag: false,
          recommendation: 'Add visible focus indicator',
        };
      }

      // Determine indicator type and visibility
      let indicatorType: 'outline' | 'background' | 'border' | 'shadow' | 'none' = 'none';
      let hasVisibleIndicator = false;

      if (focusStyles.outline && focusStyles.outline !== 'none') {
        indicatorType = 'outline';
        hasVisibleIndicator = true;
      } else if (focusStyles.boxShadow && focusStyles.boxShadow !== 'none') {
        indicatorType = 'shadow';
        hasVisibleIndicator = true;
      } else if (focusStyles.borderWidth && parseFloat(focusStyles.borderWidth) > 0) {
        indicatorType = 'border';
        hasVisibleIndicator = true;
      } else if (focusStyles.backgroundColor && focusStyles.backgroundColor !== 'transparent') {
        indicatorType = 'background';
        hasVisibleIndicator = true;
      }

      // Basic WCAG compliance check (simplified)
      const meetsWcag = hasVisibleIndicator;

      return {
        hasVisibleIndicator,
        indicatorType,
        meetsWcag,
        recommendation: meetsWcag
          ? undefined
          : 'Add visible focus indicator with sufficient contrast',
      };
    } catch (error) {
      console.error('Error analyzing focus visibility:', error);
      return {
        hasVisibleIndicator: false,
        indicatorType: 'none',
        meetsWcag: false,
        recommendation: 'Unable to analyze focus visibility',
      };
    }
  }

  /**
   * Generate comprehensive focus analysis report
   */
  static async generateFocusReport(page: Page): Promise<FocusAnalysisResult> {
    const focusOrder = await this.analyzeFocusOrder(page);
    const focusableElements = await this.getFocusableElements(page);

    // Analyze focus visibility for first few elements
    const visibilityResults: FocusVisibilityResult[] = [];
    for (let i = 0; i < Math.min(5, focusableElements.length); i++) {
      const visibility = await this.analyzeFocusVisibility(page, focusableElements[i]);
      visibilityResults.push(visibility);
    }

    const hasVisibleFocus = visibilityResults.some((result) => result.hasVisibleIndicator);

    return {
      elementSelector: 'body', // Overall page analysis
      elementType: 'page',
      isFocusable: true,
      tabIndex: 0,
      hasVisibleFocus: hasVisibleFocus,
      focusIndicatorWidth: 0,
      focusIndicatorColor: '',
      focusContrastRatio: 0,
      isObscured: false,
      keyboardAccessible: focusOrder.elements.length > 0,
      issues: focusOrder.issues,
    };
  }
}
