/**
 * TEST AUTH ROUTES - DEVELOPMENT ONLY
 *
 * This file contains routes designed for testing Keycloak authentication during development.
 * These routes are only registered when NODE_ENV is set to 'development'.
 *
 * IMPORTANT: For production, authentication should be implemented in dedicated auth routes.
 * These test routes should NOT be used in production as they may expose sensitive information.
 *
 * The patterns implemented here can be used as reference for the proper authentication
 * implementation in the frontend/app/auth/ routes.
 */

import express, { Router, Request, Response } from 'express';
import { keycloak } from '../lib/keycloak'; // Import keycloak instance
import { env } from '../../../lib/env'; // Import validated environment variables

// Ensure we are in development mode
if (env.NODE_ENV !== 'development') {
  console.warn('[WARNING] test-auth routes should only be used in development');
}

const router: Router = express.Router();

/**
 * Login route - redirects to Keycloak login page
 * This is a special route that doesn't need keycloak.protect() because
 * it's meant to initiate the login flow
 */
router.get('/login', (req: Request, res: Response) => {
  // Redirect to the default Keycloak login page
  // The returnTo query parameter can be used to specify where to redirect after login
  const returnTo = (req.query.returnTo as string) || '/api/v1/test-auth';

  // Server base URL (for redirect_uri)
  const serverBase = `http://localhost:${env.BACKEND_PORT}`;

  // Manually construct the Keycloak URL to avoid interpolation issues
  const keycloakServerUrl = `http://localhost:${env.KEYCLOAK_PORT}/auth`;

  // Generate login URL and redirect using the validated environment variables
  const loginUrl = `${keycloakServerUrl}/realms/${env.KEYCLOAK_REALM}/protocol/openid-connect/auth?client_id=${env.KEYCLOAK_CLIENT_ID_BACKEND}&redirect_uri=${encodeURIComponent(serverBase + returnTo)}&response_type=code&scope=openid`;

  console.log('Redirecting to Keycloak login:', loginUrl);
  res.redirect(loginUrl);
});

/**
 * Logout route
 */
router.get('/logout', (req: Request, res: Response) => {
  // If using Keycloak's logout functionality
  if (req.session) {
    req.session.destroy(() => {
      res.redirect('/api/v1/test-auth/login');
    });
  } else {
    res.redirect('/api/v1/test-auth/login');
  }
});

/**
 * Protected route
 * This route requires authentication via Keycloak
 */
router.get('/', keycloak.protect(), (req: Request, res: Response) => {
  // If the user is authenticated, Keycloak adds user information to req.kauth
  try {
    // @ts-expect-error kauth is injected by keycloak-connect
    const username = req.kauth?.grant?.access_token?.content?.preferred_username || 'Unknown User';
    // @ts-expect-error kauth is injected by keycloak-connect
    const roles = req.kauth?.grant?.access_token?.content?.realm_access?.roles || [];

    // Return user information
    res.json({
      message: 'This is a protected route. You are authenticated!',
      user: username,
      roles: roles,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error accessing user information:', error);
    res.status(500).json({
      message: 'Error retrieving authenticated user information',
      error: error instanceof Error ? error.message : String(error),
      note: 'You may be authenticated but there was an error processing your session',
    });
  }
});

/**
 * Info route - provides information about Keycloak configuration
 * Useful for debugging purposes
 */
router.get('/info', (req: Request, res: Response) => {
  // Manually construct the Keycloak URL to avoid interpolation issues
  const keycloakServerUrl = `http://localhost:${env.KEYCLOAK_PORT}/auth`;

  res.json({
    message: 'Keycloak configuration information',
    realm: env.KEYCLOAK_REALM,
    authServerUrl: keycloakServerUrl,
    clientId: env.KEYCLOAK_CLIENT_ID_BACKEND,
    publicClient: false,
    bearerOnly: false,
    // Config debugging
    port: env.KEYCLOAK_PORT,
    // Safe to expose these in development environments, remove for production
    note: 'This endpoint should be disabled in production',
  });
});

export default router;
