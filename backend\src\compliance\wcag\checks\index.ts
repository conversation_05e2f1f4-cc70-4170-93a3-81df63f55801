/**
 * WCAG Checks Index
 * Exports all automated WCAG checks
 */

// Import all check classes
import { ContrastMinimumCheck } from './contrast-minimum';
import { FocusVisibleCheck } from './focus-visible';
import { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
import { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
import { FocusAppearanceCheck } from './focus-appearance';
import { TargetSizeCheck } from './target-size';
import { NonTextContentCheck } from './non-text-content';
import { InfoRelationshipsCheck } from './info-relationships';
import { KeyboardCheck } from './keyboard';
import { ErrorIdentificationCheck } from './error-identification';
import { NameRoleValueCheck } from './name-role-value';
import { RedundantEntryCheck } from './redundant-entry';
import { ImageAlternatives3Check } from './image-alternatives-3';
import { KeyboardFocus3Check } from './keyboard-focus-3';
import { CaptionsCheck } from './captions';
import { FocusOrderCheck } from './focus-order';
import { DraggingMovementsCheck } from './dragging-movements';
import { ConsistentHelpCheck } from './consistent-help';
import { TextWordingCheck } from './text-wording';
import { MotorCheck } from './motor';
import { PronunciationMeaningCheck } from './pronunciation-meaning';

// Re-export all check classes
export { ContrastMinimumCheck } from './contrast-minimum';
export { FocusVisibleCheck } from './focus-visible';
export { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
export { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
export { FocusAppearanceCheck } from './focus-appearance';
export { TargetSizeCheck } from './target-size';
export { NonTextContentCheck } from './non-text-content';
export { InfoRelationshipsCheck } from './info-relationships';
export { KeyboardCheck } from './keyboard';
export { ErrorIdentificationCheck } from './error-identification';
export { NameRoleValueCheck } from './name-role-value';
export { RedundantEntryCheck } from './redundant-entry';
export { ImageAlternatives3Check } from './image-alternatives-3';
export { KeyboardFocus3Check } from './keyboard-focus-3';
export { CaptionsCheck } from './captions';
export { FocusOrderCheck } from './focus-order';
export { DraggingMovementsCheck } from './dragging-movements';
export { ConsistentHelpCheck } from './consistent-help';
export { TextWordingCheck } from './text-wording';
export { MotorCheck } from './motor';
export { PronunciationMeaningCheck } from './pronunciation-meaning';

// Check registry for easy access
export const AUTOMATED_CHECKS = {
  // Part 3: Fully Automated (100%)
  'WCAG-004': 'ContrastMinimumCheck',
  'WCAG-007': 'FocusVisibleCheck',
  'WCAG-010': 'FocusNotObscuredMinimumCheck',
  'WCAG-011': 'FocusNotObscuredEnhancedCheck',
  'WCAG-012': 'FocusAppearanceCheck',
  'WCAG-014': 'TargetSizeCheck',

  // Part 4: Very High Automation (85-95%)
  'WCAG-001': 'NonTextContentCheck',
  'WCAG-003': 'InfoRelationshipsCheck',
  'WCAG-005': 'KeyboardCheck',
  'WCAG-008': 'ErrorIdentificationCheck',
  'WCAG-009': 'NameRoleValueCheck',
  'WCAG-016': 'RedundantEntryCheck',
  'WCAG-017': 'ImageAlternatives3Check',
  'WCAG-019': 'KeyboardFocus3Check',

  // Part 5: High & Medium Automation (60-80%)
  'WCAG-002': 'CaptionsCheck',
  'WCAG-006': 'FocusOrderCheck',
  'WCAG-013': 'DraggingMovementsCheck',
  'WCAG-015': 'ConsistentHelpCheck',
  'WCAG-018': 'TextWordingCheck',
  'WCAG-020': 'MotorCheck',
  'WCAG-021': 'PronunciationMeaningCheck',
} as const;

/**
 * Get check implementation by rule ID
 */
export function getCheckImplementation(ruleId: string) {
  // Dynamic import approach to avoid circular dependencies
  switch (ruleId) {
    // Part 3: Fully Automated (100%)
    case 'WCAG-004':
      return ContrastMinimumCheck;
    case 'WCAG-007':
      return FocusVisibleCheck;
    case 'WCAG-010':
      return FocusNotObscuredMinimumCheck;
    case 'WCAG-011':
      return FocusNotObscuredEnhancedCheck;
    case 'WCAG-012':
      return FocusAppearanceCheck;
    case 'WCAG-014':
      return TargetSizeCheck;

    // Part 4: Very High Automation (85-95%)
    case 'WCAG-001':
      return NonTextContentCheck;
    case 'WCAG-003':
      return InfoRelationshipsCheck;
    case 'WCAG-005':
      return KeyboardCheck;
    case 'WCAG-008':
      return ErrorIdentificationCheck;
    case 'WCAG-009':
      return NameRoleValueCheck;
    case 'WCAG-016':
      return RedundantEntryCheck;
    case 'WCAG-017':
      return ImageAlternatives3Check;
    case 'WCAG-019':
      return KeyboardFocus3Check;

    // Part 5: High & Medium Automation (60-80%)
    case 'WCAG-002':
      return CaptionsCheck;
    case 'WCAG-006':
      return FocusOrderCheck;
    case 'WCAG-013':
      return DraggingMovementsCheck;
    case 'WCAG-015':
      return ConsistentHelpCheck;
    case 'WCAG-018':
      return TextWordingCheck;
    case 'WCAG-020':
      return MotorCheck;
    case 'WCAG-021':
      return PronunciationMeaningCheck;

    default:
      return undefined;
  }
}

/**
 * Get automation level for a rule
 */
export function getAutomationLevel(ruleId: string): number {
  const automationLevels: Record<string, number> = {
    'WCAG-001': 0.95,
    'WCAG-002': 0.8,
    'WCAG-003': 0.9,
    'WCAG-004': 1.0,
    'WCAG-005': 0.85,
    'WCAG-006': 0.75,
    'WCAG-007': 1.0,
    'WCAG-008': 0.9,
    'WCAG-009': 0.9,
    'WCAG-010': 1.0,
    'WCAG-011': 1.0,
    'WCAG-012': 1.0,
    'WCAG-013': 0.7,
    'WCAG-014': 1.0,
    'WCAG-015': 0.8,
    'WCAG-016': 0.85,
    'WCAG-017': 0.95,
    'WCAG-018': 0.75,
    'WCAG-019': 0.9,
    'WCAG-020': 0.8,
    'WCAG-021': 0.6,
  };
  return automationLevels[ruleId] || 0;
}

export type AutomatedCheckType = keyof typeof AUTOMATED_CHECKS;
