// backend/src/services/scan-service.ts
import { v4 as uuidv4 } from 'uuid';
import db from '../lib/db'; // Corrected path to the shared Knex instance
import { HipaaCompliance, GdprCompliance, AdaCompliance } from '../compliance';
import { SCAN_STATUS, ERROR_MESSAGES } from '../lib/constants';
import { User, Scan, ComplianceFinding, ScanWithFindings } from '../types';
import { HipaaSeverity } from '../compliance/hipaa/privacy/types';
import { HipaaSecurityOrchestrator } from '../compliance/hipaa/security/hipaa-security-orchestrator';
import { HipaaSecurityScanResult } from '../compliance/hipaa/security/types';
import { GdprOrchestrator } from '../compliance/gdpr/orchestrator';
import { GdprScanRequest, GdprScanResult } from '../compliance/gdpr/types';

/**
 * Custom error for cases where a user is not found.
 */
export class UserNotFoundError extends Error {
  /**
   * Constructs a UserNotFoundError.
   * @param {string} message - The error message.
   */
  constructor(message: string) {
    super(message);
    this.name = 'UserNotFoundError';
  }
}

/**
 * Custom error for issues occurring during the scan processing.
 */
export class ScanProcessingError extends Error {
  public scanId?: string;
  /**
   * Constructs a ScanProcessingError.
   * @param {string} message - The error message.
   * @param {string} [scanId] - The ID of the scan, if available, when the error occurred.
   */
  constructor(message: string, scanId?: string) {
    super(message);
    this.name = 'ScanProcessingError';
    this.scanId = scanId;
  }
}

/**
 * @class ScanService
 * @description Service class for handling compliance scan operations.
 */
class ScanService {
  private hipaaSecurityOrchestrator: HipaaSecurityOrchestrator;

  constructor() {
    this.hipaaSecurityOrchestrator = new HipaaSecurityOrchestrator();
  }
  /**
   * Initiates a new compliance scan for a given URL and set of standards.
   *
   * @param {string} keycloakUserId - The Keycloak ID of the user initiating the scan.
   * @param {string} urlToScan - The URL to be scanned.
   * @param {string[]} standards - An array of compliance standards to scan against (e.g., ['hipaa', 'gdpr']).
   * @returns {Promise<ScanWithFindings>} A promise that resolves to the created scan record with its findings.
   * @throws {UserNotFoundError} If the user associated with keycloakUserId is not found.
   * @throws {ScanProcessingError} If any other error occurs during the scan process.
   */
  public async initiateNewScan(
    keycloakUserId: string, // Changed to keycloakUserId for clarity
    urlToScan: string,
    standards: string[],
  ): Promise<ScanWithFindings> {
    console.log(
      `ScanService: Initiating new scan for Keycloak user ${keycloakUserId}, URL: ${urlToScan}, Standards: [${standards.join(', ')}]`,
    );
    let scanId: string | undefined = undefined;

    try {
      // 1. Fetch User ID from Keycloak User ID
      const user: User | undefined = await db('users')
        .where({ keycloak_id: keycloakUserId })
        .first();

      if (!user || !user.id) {
        console.warn(`ScanService: User not found for Keycloak ID: ${keycloakUserId}`);
        throw new UserNotFoundError(ERROR_MESSAGES.USER_NOT_FOUND);
      }
      console.log(`ScanService: User found with ID: ${user.id}`);

      // 2. Create initial scan record
      try {
        const [initialScanRecordData]: Scan[] = await db('scans')
          .insert({
            user_id: user.id,
            url: urlToScan,
            standards_scanned: JSON.stringify(standards),
            status: SCAN_STATUS.PENDING,
            // created_at and updated_at are handled by DB or Knex
          })
          .returning('*');

        if (!initialScanRecordData || !initialScanRecordData.id) {
          console.error('ScanService: Scan creation did not return a valid record or ID.');
          // This error will be caught by the catch block below
          throw new Error('Scan record creation failed internally.');
        }
        scanId = initialScanRecordData.id; // Assign scanId here
        console.log(`ScanService: Scan record created with ID: ${scanId}`);
      } catch (dbError: unknown) {
        console.error(
          'ScanService: Error during initial scan record creation:',
          dbError instanceof Error ? dbError.message : String(dbError),
        );
        // Ensure scanId is passed, even if undefined, to the ScanProcessingError
        throw new ScanProcessingError(ERROR_MESSAGES.SCAN_CREATION_FAILED, scanId);
      }

      // 3. Update scan status to IN_PROGRESS
      // This part should only execute if scanId is successfully assigned.
      try {
        // Ensure scanId is defined before attempting to update
        if (!scanId) {
          // This should not happen if the above block succeeded, but as a safeguard.
          console.error('ScanService: Cannot update scan to IN_PROGRESS, scanId is undefined.');
          throw new Error('Cannot update scan to IN_PROGRESS due to missing scanId.');
        }
        await db('scans').where({ id: scanId }).update({ status: SCAN_STATUS.IN_PROGRESS });
        console.log(`ScanService: Scan ${scanId} status updated to IN_PROGRESS.`);
      } catch (updateError: unknown) {
        console.error(
          `ScanService: Error updating scan ${scanId} to IN_PROGRESS:`,
          updateError instanceof Error ? updateError.message : String(updateError),
        );
        // Re-throw to be caught by the main try-catch, which will set status to FAILED.
        // The main catch block will use the message from this updateError.
        throw updateError;
      }

      const allFindings: ComplianceFinding[] = [];

      // 4. Perform compliance checks based on selected standards
      // Ensure scanId is valid before proceeding
      if (!scanId) {
        // This should be caught earlier, but as a final safeguard before compliance checks
        console.error('ScanService: scanId is undefined before compliance checks.');
        throw new ScanProcessingError(ERROR_MESSAGES.SCAN_NOT_FOUND, 'unknown');
      }

      if (standards.includes('hipaa')) {
        console.log(`ScanService: Performing HIPAA checks for scan ${scanId}`);

        // Feature flag for enhanced HIPAA module (default: enabled)
        const useEnhancedHipaa = process.env.ENABLE_ENHANCED_HIPAA !== 'false';

        if (useEnhancedHipaa) {
          console.log(`ScanService: Using enhanced HIPAA orchestrator for scan ${scanId}`);

          try {
            // Import the enhanced orchestrator
            const { HipaaPrivacyPolicyOrchestrator, HipaaDatabase } = await import(
              '../compliance/hipaa/privacy'
            );

            // Perform comprehensive HIPAA scan
            const orchestrator = new HipaaPrivacyPolicyOrchestrator();
            const hipaaResult = await orchestrator.performComprehensiveScan(urlToScan, {
              timeout: 30000,
              enableLevel1: true,
              enableLevel2: true,
              enableLevel3: true,
              cacheResults: true,
            });

            console.log(
              `ScanService: Enhanced HIPAA scan completed with ${hipaaResult.checks.length} checks and ${hipaaResult.overallScore}% compliance score`,
            );

            // Store enhanced results in new database schema
            console.log(
              `ScanService: Saving enhanced HIPAA results to database for scan ${scanId}...`,
            );
            const hipaaScanId = await HipaaDatabase.saveScanResult(scanId, hipaaResult);
            console.log(
              `ScanService: Enhanced HIPAA results saved with hipaa_scan_id: ${hipaaScanId}`,
            );

            // Verify the enhanced results were saved correctly
            console.log(
              `ScanService: Verifying enhanced HIPAA results storage for scan ${scanId}...`,
            );
            const verificationResult = await HipaaDatabase.getScanResult(hipaaScanId);
            if (!verificationResult) {
              throw new Error(`Failed to verify enhanced HIPAA results storage for scan ${scanId}`);
            }
            console.log(
              `ScanService: Enhanced HIPAA results verification successful - ${verificationResult.checks.length} checks stored`,
            );

            // Transform enhanced results to legacy format for backward compatibility
            hipaaResult.checks.forEach((check) => {
              allFindings.push({
                id: uuidv4(),
                scan_id: scanId || 'unknown',
                standard: 'HIPAA',
                check_id: check.checkId,
                description: check.description || `${check.name} check`,
                passed: check.passed,
                severity: this.mapHipaaSeverityToLegacy(check.severity),
                details: {
                  summary: check.details.summary,
                  confidence: check.confidence,
                  overallScore: check.overallScore,
                  analysisLevels: check.metadata?.analysisLevels || [],
                  processingTime: check.metadata?.processingTime || 0,
                },
                created_at: new Date().toISOString(),
              });
            });
          } catch (enhancedError) {
            console.error(
              `ScanService: Enhanced HIPAA scan failed, falling back to legacy:`,
              enhancedError,
            );

            // Fallback to legacy HIPAA check
            const hipaaResult = await HipaaCompliance.checkPrivacyPolicyPresence(urlToScan);
            allFindings.push({
              id: uuidv4(),
              scan_id: scanId || 'unknown',
              standard: 'HIPAA',
              check_id: hipaaResult.checkId,
              description: hipaaResult.description || `${hipaaResult.name} check (legacy fallback)`,
              passed: hipaaResult.passed,
              severity: 'high',
              details: {
                ...hipaaResult.details,
                fallbackReason: 'Enhanced HIPAA scan failed',
                error: enhancedError instanceof Error ? enhancedError.message : 'Unknown error',
              },
              created_at: new Date().toISOString(),
            });
          }
        } else {
          console.log(`ScanService: Using legacy HIPAA check for scan ${scanId}`);

          // Legacy HIPAA check
          const hipaaResult = await HipaaCompliance.checkPrivacyPolicyPresence(urlToScan);
          allFindings.push({
            id: uuidv4(),
            scan_id: scanId || 'unknown',
            standard: 'HIPAA',
            check_id: hipaaResult.checkId,
            description: hipaaResult.description || `${hipaaResult.name} check`,
            passed: hipaaResult.passed,
            severity: 'high',
            details: hipaaResult.details || {},
            created_at: new Date().toISOString(),
          });
        }
      }
      if (standards.includes('gdpr')) {
        console.log(`ScanService: Performing GDPR checks for scan ${scanId}`);
        // Fixed: Using the correct function that exists in the GDPR module with correct parameter count
        const gdprCookieResult = await GdprCompliance.checkCookieConsent(urlToScan);
        // Transform GdprCheckResult to ComplianceFinding
        allFindings.push({
          id: uuidv4(),
          scan_id: scanId || 'unknown',
          standard: 'GDPR',
          check_id: gdprCookieResult.checkId,
          description: gdprCookieResult.description,
          passed: gdprCookieResult.passed,
          severity: 'medium', // Default severity for GDPR
          details: gdprCookieResult.details || {},
          created_at: new Date().toISOString(),
        });
      }
      if (standards.includes('ada')) {
        console.log(`ScanService: Performing ADA checks for scan ${scanId}`);
        // Fixed: Use the actual function that exists in the ADA module with correct parameter count
        const adaImageAltResult = await AdaCompliance.checkImageAltText(urlToScan);
        // Transform AdaCheckResult to ComplianceFinding
        allFindings.push({
          id: uuidv4(),
          scan_id: scanId || 'unknown',
          standard: 'ADA',
          check_id: adaImageAltResult.checkId,
          description: adaImageAltResult.description,
          passed: adaImageAltResult.passed,
          severity: 'high', // Default severity for ADA
          details: { details: adaImageAltResult.details }, // Wrap the array in an object to match expected type
          created_at: new Date().toISOString(),
        });
      }
      if (standards.includes('wcag')) {
        console.log(`ScanService: Performing WCAG checks for scan ${scanId}`);
        // TODO: WCAG implementation will be added in subsequent parts
        // const wcagTitleResult = await WcagCompliance.checkPageTitlePresence(urlToScan);
        // Transform WcagCheckResult to ComplianceFinding
        console.log('WCAG scanning not yet implemented - Part 1 foundation only');
        /*
        allFindings.push({
          id: uuidv4(),
          scan_id: scanId || 'unknown',
          standard: 'WCAG',
          check_id: wcagTitleResult.checkId,
          description: wcagTitleResult.description,
          passed: wcagTitleResult.passed,
          severity: 'high', // Default severity for WCAG issues
          details:
            typeof wcagTitleResult.details === 'string'
              ? wcagTitleResult.details
              : (wcagTitleResult.details as Record<string, unknown>),
          created_at: new Date().toISOString(),
        });
        */
      }

      // 5. Insert all findings into the database
      if (allFindings.length > 0) {
        console.log(`ScanService: Inserting ${allFindings.length} findings for scan ${scanId}`);
        await db('compliance_findings').insert(allFindings);
      }

      // 6. Update scan status to COMPLETED
      await db('scans')
        .where({ id: scanId })
        .update({ status: SCAN_STATUS.COMPLETED, error_message: null });
      console.log(`ScanService: Scan ${scanId} status updated to COMPLETED.`);

      // 7. Fetch the final scan record with its findings
      const finalScan: Scan | undefined = await db('scans').where({ id: scanId }).first();
      const findingsFromDb: ComplianceFinding[] = await db('compliance_findings').where({
        scan_id: scanId || 'unknown',
      });

      if (!finalScan) {
        console.error(`ScanService: Failed to retrieve final scan record for ID ${scanId}`);
        throw new ScanProcessingError(ERROR_MESSAGES.SCAN_NOT_FOUND, scanId);
      }

      console.log(`ScanService: Scan ${scanId} completed successfully.`);
      return { ...finalScan, findings: findingsFromDb };
    } catch (error: unknown) {
      let errorMessage = ERROR_MESSAGES.SCAN_FAILED_INTERNAL;
      let capturedScanId = scanId; // Use the scanId captured in the outer scope

      if (error instanceof ScanProcessingError) {
        errorMessage = error.message;
        // If ScanProcessingError already has a scanId, prefer it.
        // This is important if the error (like SCAN_CREATION_FAILED) happened before outer scanId was set.
        capturedScanId = error.scanId || scanId;
      } else if (error instanceof UserNotFoundError) {
        // UserNotFoundError should not attempt to update scan status as scanId might not exist.
        console.error(`ScanService: UserNotFoundError caught: ${error.message}`);
        throw error; // Re-throw UserNotFoundError as is
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      console.error(
        `ScanService: Error during scan process for URL ${urlToScan} (Scan ID: ${capturedScanId || 'N/A'}):`,
        error,
      );

      if (capturedScanId) {
        try {
          await db('scans')
            .where({ id: capturedScanId })
            .update({ status: SCAN_STATUS.FAILED, error_message: errorMessage });
          console.error(
            `ScanService: Scan ${capturedScanId} marked as failed due to error: ${errorMessage}`,
          );
        } catch (dbUpdateError) {
          console.error(
            `ScanService: Failed to update scan status to FAILED for scan ID ${capturedScanId}:`,
            dbUpdateError,
          );
        }
      }
      // Re-throw a service-specific error to be handled by the route
      // Ensure we use the capturedScanId that might have come from an inner ScanProcessingError
      throw new ScanProcessingError(errorMessage, capturedScanId);
    }
  }

  /**
   * Perform HIPAA Security scan using the new orchestrator
   */
  async performHipaaSecurityScan(
    scanId: string,
    urlToScan: string,
    standards: string[],
  ): Promise<HipaaSecurityScanResult | null> {
    if (!standards.includes('hipaa-security')) {
      return null;
    }

    console.log(`ScanService: Performing HIPAA Security scan for ${scanId}`);

    try {
      const result = await this.hipaaSecurityOrchestrator.performComprehensiveScan(urlToScan, {
        timeout: 300000, // 5 minutes
        maxPages: 15,
        scanDepth: 2,
        enableVulnerabilityScanning: true,
        enableSSLAnalysis: true,
        enableContentAnalysis: true,
      });

      console.log(`ScanService: HIPAA Security scan completed with ${result.overallScore}% score`);
      return result;
    } catch (error) {
      console.error(`ScanService: HIPAA Security scan failed:`, error);
      throw error;
    }
  }

  /**
   * Map HIPAA severity enum to legacy string format
   */
  private mapHipaaSeverityToLegacy(
    severity: HipaaSeverity,
  ): 'critical' | 'high' | 'medium' | 'low' | 'informational' {
    const severityMap: Record<string, string> = {
      critical: 'critical',
      high: 'high',
      medium: 'medium',
      low: 'low',
      info: 'low',
    };
    return (
      (severityMap[severity] as 'critical' | 'high' | 'medium' | 'low' | 'informational') ||
      'medium'
    );
  }

  /**
   * Perform GDPR compliance scan using the new orchestrator
   * REAL SCANNING ONLY - no mock data
   */
  async performGdprScan(userId: string, scanRequest: GdprScanRequest): Promise<GdprScanResult> {
    console.log(`🔍 ScanService: Starting GDPR scan for ${scanRequest.targetUrl}`);

    try {
      const orchestrator = new GdprOrchestrator();
      const result = await orchestrator.performComprehensiveScan(userId, scanRequest);

      console.log(
        `✅ ScanService: GDPR scan completed successfully - Score: ${result.overallScore}%, Risk: ${result.riskLevel}`,
      );
      return result;
    } catch (error) {
      console.error(`❌ ScanService: GDPR scan failed:`, error);
      throw new Error(
        `GDPR scan failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get supported scan types including GDPR
   */
  getSupportedScanTypes(): string[] {
    return ['hipaa-privacy', 'hipaa-security', 'gdpr', 'wcag', 'ada'];
  }
}

export default new ScanService();
